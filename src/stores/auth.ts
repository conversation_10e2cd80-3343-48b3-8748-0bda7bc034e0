import { defineStore } from 'pinia'
import { useAPI } from '@/composables/useAPI'
import { useSettingsStore } from '@/stores/settings'

export const useAuthStore = defineStore('chatbotAuthStore', {
  persist: [
    {
      pick: ['chatbotAuths', 'tenantId', 'envId', 'env', 'liffId'],
      storage: sessionStorage,
    },
  ],
  state: () => ({
    env: '',
    tenantId: '',
    envId: '',
    // chatbot_token: '',
    // chatbot_refresh_token: '',
    liffId: '',
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>,
    chatbotAuths: {} as Record<string, any>,
  }),
  getters: {
    tokenKey: (state) => state.tenantId + (state.envId || state.env || ''),
    isLoggedIn(): boolean {
      return this.chatbotAuths[this.tokenKey]?.token ? true : false
    },
    isNeedLogin(): boolean {
      return !this.chatbotAuths[this.tokenKey] && (this.env || this.envId) ? true : false
    },
    chatbot_token(): string {
      return this.chatbotAuths[this.tokenKey]?.token || ''
    },
    chatbot_refresh_token(): string {
      return this.chatbotAuths[this.tokenKey]?.refresh_token || ''
    },
  },
  actions: {
    initChatbot(
      options: {
        tenantId?: string
        envId?: string
        env?: string
        token?: string
        refresh_token?: string
        liffId?: string
      } = {},
    ) {
      const scripts = document.getElementsByTagName('script')
      for (let i = 0; i < scripts.length; i++) {
        const script = scripts[i]
        let src = script.getAttribute('src')
        if (src && (src.includes('chatbot.min') || src.includes('dev.ts'))) {
          console.log('🚀 ~ src:', src)
          // check if src include https
          if (src.startsWith('http')) {
            src = src
          } else {
            src = window.location.origin + src
          }
          // check if src is invalid URL return
          try {
            new URL(src)
          } catch (error) {
            return
          }
          const url = new URL(src)
          const tenantId = url.searchParams.get('tenantId')
          console.log('🚀 ~ tenantId:', tenantId)

          const envId = url.searchParams.get('envId')
          const env = url.searchParams.get('env')
          const refresh_token = url.searchParams.get('refresh_token')
          const token = url.searchParams.get('token')
          const liffId = url.searchParams.get('liffId')
          if (tenantId) {
            this.tenantId = tenantId
          }
          if (envId) {
            this.envId = envId
          }
          if (env) {
            this.env = env
          }
          if (refresh_token) {
            // this.chatbot_refresh_token = refresh_token
            // this.chatbot_token = token || ''
            this.chatbotAuths[this.tokenKey] = {
              refresh_token,
              token,
            }
          }
          if (liffId) {
            this.liffId = liffId
          }
          break
        }
      }
      if (options.tenantId) {
        this.tenantId = options.tenantId
      }
      if (options.envId) {
        this.envId = options.envId
      }
      if (options.env) {
        this.env = options.env
      }
      if (options.refresh_token) {
        // this.chatbot_refresh_token = options.refresh_token
        // this.chatbot_token = options.token || ''
        this.chatbotAuths[this.tokenKey] = {
          refresh_token: options.refresh_token,
          token: options.token,
        }
      }
    },
    async guestLogin(): Promise<any> {
      const settingsStore = useSettingsStore()
      if (!this.tenantId) return true
      try {
        // get tenantId from url
        this.loadings.guestLogin = true
        this.errors.guestLogin = null
        const response = await useAPI().ragService.post('/login/guest', {
          tenant_id: this.tenantId,
        })
        // this.chatbot_token = response.data?.token
        // this.chatbot_refresh_token = response.data?.refresh_token
        this.chatbotAuths[this.tokenKey] = {
          token: response.data?.token,
          refresh_token: response.data?.refresh_token,
        }
        await settingsStore.getBasicSettings()
        return response.data as any
      } catch (error: any) {
        this.errors.guestLogin = error?.response?.data || error
        console.error('Guest login error:', error)

        // Display error message to the user
        settingsStore.setErrorMessage(
          'ゲストログインに失敗しました。しばらくしてからもう一度お試しください。',
        )

        return false
      } finally {
        this.loadings.guestLogin = false
      }
    },
    async normalLogin(payload: { username: string; password: string }): Promise<any> {
      const settingsStore = useSettingsStore()
      try {
        this.loadings.normalLogin = true
        this.errors.normalLogin = null
        const response = await useAPI().ragService.post('/login', {
          tenant_id: this.tenantId,
          username: payload.username,
          password: payload.password,
        })
        // this.chatbot_token = response.data?.token
        // this.chatbot_refresh_token = response.data?.refresh_token
        this.chatbotAuths[this.tokenKey] = {
          token: response.data?.token,
          refresh_token: response.data?.refresh_token,
        }
        await settingsStore.getBasicSettings()
        return response.data as any
      } catch (error: any) {
        this.errors.normalLogin = error?.response?.data || error
        return false
      } finally {
        this.loadings.normalLogin = false
      }
    },
    async refreshToken(): Promise<any> {
      try {
        this.loadings.refreshToken = true
        this.errors.refreshToken = null
        const response = await useAPI().ragService.post('/login/refresh', {})
        // this.chatbot_token = response.data?.token
        // this.chatbot_refresh_token = response.data?.refresh_token
        this.chatbotAuths[this.tokenKey] = {
          token: response.data?.token,
          refresh_token: response.data?.refresh_token,
        }
        return response.data as any
      } catch (error: any) {
        this.errors.refreshToken = error?.response?.data || error
        return false
      } finally {
        this.loadings.refreshToken = false
      }
    },
    logout() {
      // this.chatbot_token = ''
      // this.chatbot_refresh_token = ''
      this.chatbotAuths[this.tokenKey] = {}
    },
    setChatbotAuths(tenantId: string, envId: string, env: string, auth: any) {
      const key = tenantId + (envId || env || '')
      this.chatbotAuths[key] = auth
    },
  },
})
