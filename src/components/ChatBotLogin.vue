<template>
  <section
    class="aiko-bg-gray-50 dark:bg-gray-900 aiko-flex aiko-flex-col aiko-items-center aiko-justify-center aiko-h-full"
  >
    <div
      class="aiko-flex aiko-flex-col aiko-items-center aiko-justify-center aiko-mx-auto aiko-w-full"
      :style="{
        paddingLeft: '10px',
        paddingRight: '10px',
        width: '95%',
      }"
    >
      <div
        class="aiko-w-full aiko-bg-white aiko-rounded-lg aiko-shadow dark:border md:mt-0 sm:max-w-md xl:p-0 dark:bg-gray-800 dark:border-gray-700"
      >
        <div class="aiko-p-6">
          <h1
            class="aiko-mb-2 aiko-text-base aiko-font-bold aiko-leading-tight aiko-tracking-tight aiko-text-gray-900 dark:text-white"
          >
            チャットボットのログイン
          </h1>
          <form class="space-y-4 aiko-relative" @submit.prevent="onSubmit">
            <div class="aiko-w-full">
              <label
                for="username"
                class="aiko-block aiko-mb-2 aiko-text-sm aiko-font-medium aiko-text-gray-900 dark:text-white"
              >
                ユーザー名
              </label>
              <input
                type="text"
                name="username"
                id="username"
                class="aiko-bg-gray-50 aiko-border aiko-border-gray-300 aiko-text-gray-900 aiko-rounded-lg aiko-p-2 aiko-text-xs dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                placeholder="<EMAIL>"
                required
                v-model="loginState.username"
                :style="{
                  width: '100%',
                }"
              />
            </div>
            <div>
              <label
                for="password"
                class="aiko-block aiko-mb-2 aiko-text-sm aiko-font-medium aiko-text-gray-900 dark:text-white"
              >
                パスワード
              </label>
              <input
                type="password"
                name="password"
                id="password"
                placeholder="••••••••"
                class="aiko-bg-gray-50 aiko-border aiko-border-gray-300 aiko-text-gray-900 aiko-rounded-lg aiko-block aiko-p-2 aiko-text-xs dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-blue-500 dark:focus:border-blue-500"
                required
                v-model="loginState.password"
                :style="{
                  width: '100%',
                }"
              />
            </div>

            <button
              type="submit"
              :disabled="loadings['normalLogin']"
              class="aiko-w-full aiko-text-white aiko-bg-sky-600 aiko-hover:bg-sky-700 aiko-focus:ring-4 aiko-focus:outline-none aiko-focus:ring-sky-300 aiko-font-medium aiko-rounded-lg aiko-text-sm aiko-px-5 aiko-py-2 aiko-text-center dark:bg-sky-600 dark:hover:bg-sky-700 dark:focus:ring-sky-800"
            >
              <Icon icon="eos-icons:loading" v-if="loadings['normalLogin']" />
              <span v-else> ログイン </span>
            </button>
          </form>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useChatStore } from '@/stores/chat'
import { useAuthStore } from '@/stores/auth'
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
const authStore = useAuthStore()
const { isNeedLogin, loadings } = storeToRefs(authStore)

const chatStore = useChatStore()
const loginState = ref({
  username: '',
  password: '',
})

const onSubmit = async () => {
  if (loginState.value.username.trim() === '' || loginState.value.password.trim() === '') {
    return
  }
  await authStore.normalLogin(loginState.value)
  chatStore.chatInit()
}
</script>
