<template>
  <div>
    <div
      class="aiko-flex aiko-items-start aiko-gap-2.5 aiko-group"
      :class="{
        'aiko-flex-row-reverse': right,
      }"
    >
      <img
        v-if="senderAvatar"
        class="aiko-w-8"
        :style="{
          marginTop: 'auto',
        }"
        :src="senderAvatar"
      />
      <Icon
        v-else-if="senderIcon"
        :icon="senderIcon"
        :alt="senderName"
        size="md"
        :ui="{
          rounded: 'rounded-full',
        }"
      />
      <div class="aiko-flex aiko-flex-col aiko-gap-1 aiko-w-fit aiko-max-w-[80%] aiko-relative">
        <!-- <div
          class="flex items-baseline gap-2"
          :class="{
            'flex-row-reverse': right,
          }"
        >
          <span
            v-if="senderName"
            class="text-xs font-semibold text-gray-900 dark:text-white truncate"
            >{{ senderName }}</span
          >
          <span class="font-normal text-gray-500 dark:text-gray-400" :style="{ fontSize: '9px' }">
            {{ datetimeFormatted }}
          </span>
        </div> -->
        <div
          class="aiko-flex aiko-flex-col aiko-leading-1.5 aiko-py-1 aiko-px-3 aiko-w-fit message-bubble"
          :class="[
            right
              ? `aiko-rounded-tl-xl aiko-rounded-br-xl aiko-rounded-b-xl aiko-border-${color_primary}-200 aiko-bg-${color_primary}-200 dark:bg-${color_primary}-300 hover:dark:bg-${color_primary}-400 aiko-self-end aiko-text-gray-900`
              : 'aiko-rounded-tr-xl aiko-rounded-tl-xl aiko-rounded-br-xl aiko-rounded-bl-xl aiko-bg-gray-100 dark:bg-gray-700',
          ]"
        >
          <div v-if="loading">
            <div class="aiko-flex aiko-items-center aiko-gap-1">
              <Icon icon="eos-icons:three-dots-loading" class="aiko-text-4xl" />
            </div>
          </div>
          <div
            class="aiko-text-xs aiko-relative aiko-top-2 aiko-font-normal aiko-break-words aiko-whitespace-pre-wrap"
            :class="{
              'aiko-pb-2 aiko--mt-0': isHtmlMessage(message),
              'aiko-mt-0 aiko-mb-0': !isHtmlMessage(message),
              'aiko-text-red-600 dark:text-red-400': isError,
              'aiko-text-gray-900 dark:text-white': !right, // Apply to all messages in dark mode
            }"
            v-html="isHtmlMessage(message) ? message : md.render(message || '')"
          />
          <div
            v-if="error_code"
            class="aiko-text-xs aiko-text-red-600 dark:text-red-400"
            :style="{ marginTop: '5px' }"
          >
            [エラーコード: {{ error_code }}]
          </div>
        </div>
        <div
          v-if="survey_options && !survey_done"
          class="aiko-flex aiko-flex-wrap aiko-gap-1 aiko-mt-0"
        >
          <div
            v-for="option in survey_options"
            :key="option.value"
            class="aiko-cursor-pointer aiko-py-1 aiko-px-3 aiko-text-xs aiko-text-gray-900 dark:text-gray-100 aiko-bg-white dark:bg-gray-700 aiko-rounded-md aiko-border aiko-border-gray-200 dark:border-gray-600"
            :class="[`aiko-!hover:border-${color_primary}-300`, 'aiko-!hover:border-sky-300']"
            @click="onSelectSurvey(option)"
          >
            {{ option.text }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { format, isToday } from 'date-fns'
import { useSettingsStore } from '@/stores/settings'
import { computed } from 'vue'
import { storeToRefs } from 'pinia'
import markdownit from 'markdown-it'
import mila from 'markdown-it-link-attributes'
import { useSurveyStore } from '@/stores/survey'
import { useChatStore } from '@/stores/chat'

const chatStore = useChatStore()
const { session_id } = storeToRefs(chatStore)
const surveyStore = useSurveyStore()
const md = markdownit()
md.use(mila, {
  attrs: {
    target: '_blank',
    rel: 'noopener',
    class: 'text-blue-500 underline a-link',
  },
})
const settingsStore = useSettingsStore()
const { color_primary } = storeToRefs(settingsStore)

const props = defineProps<{
  chat_id?: string
  message?: string
  senderName: string
  senderAvatar?: string
  senderIcon?: string
  datetime?: string
  right?: boolean
  loading?: boolean
  survey_options?: any[]
  survey_done?: boolean
  messageType?: string
  error_code?: string
}>()

const datetimeFormatted = computed(() => {
  if (props.datetime) {
    // check if isToday
    return isToday(new Date(props.datetime))
      ? format(new Date(props.datetime), 'HH:mm:ss')
      : format(new Date(props.datetime), 'MM月dd日 HH:mm:ss')
  }

  return ''
})

const isHtmlMessage = (message: string | undefined) => {
  return /<[^>]+>/.test(message || '')
}

const onSelectSurvey = (option: any) => {
  surveyStore.submitSurvey(session_id.value, props.chat_id as string, option)
}

const isError = computed(() => {
  return props.messageType === 'error'
})
</script>

<style lang="scss">
.message-bubble {
  opacity: 1 !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  user-select: text !important;

  a {
    color: #1565c0 !important;
    text-decoration: underline;
    cursor: pointer;
  }
}

/* Dark mode styles for message bubble */
.dark .message-bubble {
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

/* Specific styles for user messages in dark mode - using stronger selectors */
.dark .message-bubble[class*='bg-'],
.dark div[class*='bg-'].message-bubble {
  color: #333 !important; /* Dark text for better contrast on light background */
}

/* Ensure all text in user messages is visible in dark mode - using stronger selectors */
.dark .message-bubble[class*='bg-'] *,
.dark div[class*='bg-'].message-bubble * {
  color: #333 !important; /* Dark text for better contrast on light background */
}

/* Specific styles for AI messages in dark mode */
.dark .message-bubble.bg-gray-100 {
  background-color: #2a2a2a !important;
  color: #f0f0f0 !important;
}

/* Ensure all text in left message bubbles (AI) is white in dark mode */
.dark .message-bubble.bg-gray-100 * {
  color: #f0f0f0 !important;
}

/* Specific elements that might need explicit styling */
.dark .message-bubble.bg-gray-100 p,
.dark .message-bubble.bg-gray-100 span,
.dark .message-bubble.bg-gray-100 div,
.dark .message-bubble.bg-gray-100 li,
.dark .message-bubble.bg-gray-100 code,
.dark .message-bubble.bg-gray-100 small,
.dark .message-bubble.bg-gray-100 strong,
.dark .message-bubble.bg-gray-100 em,
.dark .message-bubble.bg-gray-100 b,
.dark .message-bubble.bg-gray-100 i,
.dark .message-bubble.bg-gray-100 u,
.dark .message-bubble.bg-gray-100 s,
.dark .message-bubble.bg-gray-100 sub,
.dark .message-bubble.bg-gray-100 sup,
.dark .message-bubble.bg-gray-100 mark,
.dark .message-bubble.bg-gray-100 h1,
.dark .message-bubble.bg-gray-100 h2,
.dark .message-bubble.bg-gray-100 h3,
.dark .message-bubble.bg-gray-100 h4,
.dark .message-bubble.bg-gray-100 h5,
.dark .message-bubble.bg-gray-100 h6,
.dark .message-bubble.bg-gray-100 blockquote,
.dark .message-bubble.bg-gray-100 cite {
  color: #f0f0f0 !important;
}

/* Style links in dark mode - left messages (AI) */
.dark .message-bubble.bg-gray-100 a {
  color: #90caf9 !important; /* Light blue for links in dark mode */
  text-decoration: underline;
}

/* Style code blocks in dark mode - left messages (AI) */
.dark .message-bubble.bg-gray-100 pre,
.dark .message-bubble.bg-gray-100 code {
  background-color: #1a1a1a !important;
  border-color: #444 !important;
  color: #f0f0f0 !important;
}

/* Style links in dark mode - right messages (User) */
.dark .message-bubble[class*='bg-'] a {
  color: #1565c0 !important; /* Darker blue for links on light background */
  text-decoration: underline;
}

/* Style code blocks in dark mode - right messages (User) */
.dark .message-bubble[class*='bg-'] pre,
.dark .message-bubble[class*='bg-'] code {
  background-color: #444 !important;
  border-color: #555 !important;
  color: #333 !important;
}

.message-bubble * {
  user-select: text !important;
  cursor: auto !important;
}

/* Make sure the message content is selectable */
.message-bubble div {
  user-select: text !important;
  cursor: auto !important;
  pointer-events: auto !important;
}

/* Survey options in dark mode */
.dark .aiko-cursor-pointer {
  background-color: #333 !important;
  color: #f0f0f0 !important;
  border-color: #555 !important;
}
</style>
