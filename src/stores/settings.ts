import { defineStore } from 'pinia'
import { useAPI } from '@/composables/useAPI'
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { useSurveyStore } from '@/stores/survey'

export const useSettingsStore = defineStore('settingsStore', {
  state: () => ({
    isOpen: false,
    isBubbleVisible: true, // Control visibility of the chat bubble
    isFullScreen: false, // Control full-screen mode of the chat window
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>,
    basicSettings: {} as any,
    errorMessage: '', // Store error messages to display to the user
  }),
  getters: {
    color_primary: (state) => {
      return state.basicSettings?.custom?.settings?.color_primary || 'sky'
    },
    chatbot_name: (state) => {
      return state.basicSettings?.basic?.name || 'AIチャットボット'
    },
    welcome_message: (state) => {
      return state.basicSettings?.basic?.welcome_message || ''
    },
    avatar_url: (state) => {
      return state.basicSettings?.custom?.settings?.avatar_url || ''
    },
    survey_option_count: (state) => {
      return state.basicSettings?.basic?.survey_option_count || 5
    },
    survey_options(): any[] {
      const survey_options = this.basicSettings?.survey_options || []
      // Ensure it's an array before using array methods
      if (!Array.isArray(survey_options)) {
        return []
      }
      // sort by value
      const sortedOptions = survey_options.sort((a: any, b: any) => a.value - b.value)
      return sortedOptions.slice(0, this.survey_option_count)
    },
    error_messages: (state) => {
      return state.basicSettings?.error_messages || []
    },
    defaultErrorMessage(): string {
      // Ensure error_messages is an array before using find
      if (!Array.isArray(this.error_messages)) {
        return 'エラーが発生しました。'
      }
      return (
        this.error_messages.find((obj: any) => obj.error_code === 9999)?.message ||
        'エラーが発生しました。'
      )
    },
  },
  actions: {
    async getBasicSettings() {
      const authStore = useAuthStore()
      // return there is no token
      if (!authStore.chatbot_token) return
      try {
        const { envId, env } = authStore
        // get tenantId from url
        this.loadings.getBasicSettings = true
        this.errors.getBasicSettings = null

        let apiUrl = '/v1/basicSettings'
        if (env) {
          apiUrl = '/v1/basicSettings/' + env
        } else if (envId) {
          apiUrl = '/v1/basicSettings/env/' + envId
        }

        const response = await useAPI().ragService.get(apiUrl)
        this.basicSettings = response.data
        // set tailwind primary color
        const color = this.basicSettings?.custom?.settings?.color_primary || 'sky'
        document.documentElement.style.setProperty('--tw-primary', color)
        return response.data as any
      } catch (error: any) {
        this.errors.getBasicSettings = error?.response?.data || error
        return false
      } finally {
        this.loadings.getBasicSettings = false
      }
    },

    /**
     * Set an error message to display to the user
     */
    setErrorMessage(message: string) {
      this.errorMessage = message

      // Automatically clear the error message after 5 seconds
      setTimeout(() => {
        this.errorMessage = ''
      }, 5000)
    },

    /**
     * Logout and clear all data in all stores
     * This function will reset all stores to their default state
     */
    logout() {
      // Reset settings store
      this.isOpen = false
      this.isFullScreen = false
      this.loadings = {}
      this.errors = {}
      this.basicSettings = {}
      this.errorMessage = ''

      // Reset auth store
      const authStore = useAuthStore()
      authStore.logout()

      // Reset chat store
      const chatStore = useChatStore()
      chatStore.session_id = null
      chatStore.helpful_flag = null
      chatStore.last_turn = false
      chatStore.chats = []
      chatStore.loadings = {}
      chatStore.errors = {}
      chatStore.query = ''
      chatStore.showConfirmReset = false

      // Reset survey store
      const surveyStore = useSurveyStore()
      surveyStore.loadings = {}
      surveyStore.errors = {}
    },
  },
})
