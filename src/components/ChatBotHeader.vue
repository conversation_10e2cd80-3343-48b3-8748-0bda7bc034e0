<template>
  <div
    class="aiko-py-3 aiko-px-2 aiko-text-white aiko-flex aiko-flex-row aiko-rounded-t-xl chat-header-transparent aiko-items-center"
    :class="[`aiko-bg-${color_primary}-600`]"
  >
    <div class="aiko-w-20">
      <Icon
        v-if="!hasLiffId"
        icon="material-symbols:close-rounded"
        class="aiko-text-2xl aiko-cursor-pointer"
        @click="onClose"
      />
    </div>
    <div
      class="aiko-w-full aiko-text-center aiko-cursor-grab aiko-select-none aiko-truncate"
      @mousedown="$emit('header-drag', $event)"
      @touchstart="$emit('header-drag', $event)"
    >
      {{ chatbot_name }}
    </div>
    <div class="aiko-w-20 aiko-flex aiko-flex-row aiko-justify-end aiko-gap-2">
      <Icon
        @click="showConfirmReset = true"
        icon="bx:reset"
        class="aiko-text-2xl aiko-text-white aiko-p-0.5 aiko-cursor-pointer aiko-rounded-full"
        title="Reset chat"
      />
      <Icon
        v-if="!hasLiffId"
        @click="toggleFullScreen"
        :icon="isFullScreen ? 'material-symbols:fullscreen-exit' : 'material-symbols:fullscreen'"
        class="aiko-text-2xl aiko-text-white aiko-p-0.5 aiko-cursor-pointer aiko-rounded-full"
        :title="isFullScreen ? 'Exit full screen' : 'Full screen'"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/settings'
import { useChatStore } from '@/stores/chat'
import { useAuthStore } from '@/stores/auth'
// Removed unused imports
import { storeToRefs } from 'pinia'
import { computed } from 'vue'

const settingsStore = useSettingsStore()
const authStore = useAuthStore()
const { color_primary, chatbot_name, isOpen, isFullScreen } = storeToRefs(settingsStore)
const { showConfirmReset } = storeToRefs(useChatStore())
const { liffId } = storeToRefs(authStore)

// Computed property to check if liffId has a value
const hasLiffId = computed(() => !!liffId.value)

// Function to toggle full-screen mode
const toggleFullScreen = () => {
  settingsStore.isFullScreen = !settingsStore.isFullScreen
}

const onClose = () => {
  isOpen.value = false
  isFullScreen.value = false
}
</script>

<style>
.chat-header-transparent {
  background-color: rgba(var(--color-primary-rgb, 14, 165, 233), 0.8) !important;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Dark mode styles for chat header */
.dark .chat-header-transparent {
  border-bottom: 1px solid rgba(50, 50, 50, 0.5);
  /* The background color is already set by the color_primary variable */
}
</style>
