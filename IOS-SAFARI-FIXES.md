# iOS Safari Chatbot Fixes

## 🐛 Vấn đề gốc

Trên iOS Safari, chatbot gặp các vấn đề sau:
- Click vào avatar mở chat window nhưng không thể chat được
- Click vào bất kỳ đâu trong chat window làm nó đóng lại
- Sau đó không thể scroll trang web phía sau
- Click vào bất kỳ chỗ nào chatbot cũng bị đóng mở liên tục

## 🔧 Các fix đã áp dụng

### 1. Event Propagation Fixes
```typescript
// Thêm stopPropagation cho chat window events
const handleWindowClick = (event: any) => {
  event.stopPropagation() // Ngăn event bubble up
}

const handleWindowMouseDown = (event: any) => {
  event.stopPropagation() // Ngăn xung đột events
  // ...
}
```

### 2. Touch Event Handling Improvements
```typescript
// Cải thiện touch events cho iOS Safari
const touchMoveHandler = (e: TouchEvent) => {
  if (isDragging) {
    e.preventDefault() // Chỉ prevent khi đang drag
    // ...
  }
}

// Cleanup touch listeners properly
const touchEndHandler = (e: TouchEvent) => {
  if (isDragging) {
    e.preventDefault()
  }
  stopDrag()
  // Clean up listeners
  window.removeEventListener('touchmove', touchMoveHandler)
  window.removeEventListener('touchend', touchEndHandler)
}
```

### 3. Click Outside Detection với Debounce
```typescript
let clickOutsideTimer: number | null = null

const handleClickOutside = (event: any) => {
  // Clear existing timer
  if (clickOutsideTimer) {
    clearTimeout(clickOutsideTimer)
  }
  
  // Debounce để tránh rapid toggling
  clickOutsideTimer = setTimeout(() => {
    if (!isFullScreen.value && isOpen.value) {
      const chatWindow = event.target.closest('.aiko-chat-window')
      const chatBubble = event.target.closest('.chat-bubble-container')
      
      if (!chatWindow && !chatBubble) {
        isOpen.value = false
      }
    }
  }, 100) // 100ms debounce
}
```

### 4. Body Scroll Locking cho iOS Safari
```typescript
// Cải thiện body scroll locking
if (isFullScreen.value) {
  document.body.style.overflow = 'hidden'
  document.body.style.position = 'fixed'
  document.body.style.width = '100%'
  document.body.style.height = '100%'
} else {
  document.body.style.overflow = ''
  document.body.style.position = ''
  document.body.style.width = ''
  document.body.style.height = ''
}
```

### 5. Avatar Button Touch Handling
```vue
<button
  @mousedown="startDrag"
  @touchstart.passive="startDrag"
  @click.stop
  type="button"
>
```

### 6. Event Listener Cleanup
```typescript
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside, true)
  document.removeEventListener('touchend', handleClickOutside, true)
  
  // Clear pending timers
  if (clickOutsideTimer) {
    clearTimeout(clickOutsideTimer)
    clickOutsideTimer = null
  }
})
```

## 🧪 Testing

Sử dụng file `test-ios-safari.html` để test:

### Test Steps:
1. **Mở chat**: Tap avatar → chat window mở
2. **Click trong chat**: Tap trong chat window → KHÔNG đóng
3. **Nhập tin nhắn**: Type trong input → hoạt động bình thường  
4. **Click ngoài chat**: Tap ngoài chat → đóng chat (chỉ khi không fullscreen)
5. **Scroll trang**: Scroll trang khi chat đóng → hoạt động bình thường
6. **Fullscreen mode**: Test trên mobile → body scroll bị lock
7. **Drag bubble**: Kéo thả avatar → mượt mà
8. **Rapid toggle**: Mở/đóng nhanh → không có vấn đề

### Expected Results:
✅ Chat window không đóng khi click bên trong  
✅ Chat window đóng khi click bên ngoài (non-fullscreen)  
✅ Page scrolling hoạt động khi chat đóng  
✅ Body scroll bị lock ở fullscreen mode  
✅ Không có rapid open/close issues  
✅ Touch events hoạt động mượt mà  
✅ Dragging hoạt động không xung đột  

## 🚀 Deployment

Để test các fixes:

1. **Build project** (cần Node.js >= 18):
   ```bash
   npm run build
   ```

2. **Mở test file**:
   ```bash
   open test-ios-safari.html
   ```

3. **Test trên iOS Safari**:
   - Mở trên iPhone/iPad với Safari
   - Thực hiện các test steps
   - Kiểm tra console logs

## 📝 Notes

- Các fixes tập trung vào iOS Safari compatibility
- Event handling được cải thiện để tránh xung đột
- Body scroll locking sử dụng `position: fixed` cho iOS
- Debounce được thêm để tránh rapid toggling
- Touch events được xử lý passive khi có thể
- Event listeners được cleanup đúng cách

## 🔄 Rollback

Nếu có vấn đề, có thể rollback bằng cách:
1. Remove `@click.stop` từ avatar button
2. Remove `event.stopPropagation()` từ window handlers  
3. Remove debounce logic từ click outside
4. Restore simple body scroll locking
