# llm-rag-chatbot-embed

A Vue.js-based embeddable chatbot that can be integrated into any website.

## Features

- Embeddable chatbot with a floating UI
- Draggable chatbot button and window
- Authentication support (guest and normal login)
- Customizable appearance
- RAG (Retrieval-Augmented Generation) capabilities

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
pnpm install
```

### Compile and Hot-Reload for Development

```sh
pnpm dev
```

### Type-Check, Compile and Minify for Production

```sh
pnpm build
```

## Usage

### Basic Usage

Include the chatbot script in your HTML:

```html
<script src="path/to/chatbot.min.js"></script>
```

The chatbot will be automatically initialized and a floating button will appear on your page.

### Using the Default Instance

The chatbot script creates a default instance that you can control:

```javascript
// Access the default instance
const chatbot = window.LLMRagChatbot.instance

// Open the chatbot
chatbot.open()

// Close the chatbot
chatbot.close()

// Toggle the chatbot
chatbot.toggle()

// Send a message
chatbot.sendMessage('Hello, chatbot!')

// Reset the chat
chatbot.resetChat()

// Logout and clear all data in stores
chatbot.logout()

// Get chat history
const history = chatbot.getChatHistory()
```

### Creating a New Instance

You can create a new instance with custom options:

```javascript
// Create a new instance
const myChatbot = new window.LLMRagChatbot.Class({
  tenantId: 'your-tenant-id',
  envId: 'your-env-id',
  env: 'your-env',
  token: 'your-token',
  refresh_token: 'your-refresh-token',
  position: { left: '20px', top: '20px' },
  windowPosition: { left: '20px' },
})

// Use the new instance
myChatbot.open()
```

## API Reference

### LLMRagChatbot Class

#### Constructor Options

- `tenantId` (string): The tenant ID
- `envId` (string): The environment ID
- `env` (string): The environment name
- `token` (string): The authentication token
- `refresh_token` (string): The refresh token
- `position` (object): The position of the chatbot button
  - `left` (string): CSS left position
  - `top` (string): CSS top position
- `windowPosition` (object): The position of the chatbot window
  - `left` (string): CSS left position

#### Methods

- `initialize(options)`: Initialize the chatbot with options
- `reload({ tenantId, envId, env, token, refresh_token })`: Reload the chatbot with new authentication parameters and reset chat history
- `updateAuth({ tenantId, envId, env, token, refresh_token })`: Update authentication parameters without resetting chat history
- `open()`: Open the chatbot
- `close()`: Close the chatbot
- `toggle()`: Toggle the chatbot open/closed state
- `hideBubble()`: Hide the chat bubble (floating button)
- `showBubble()`: Show the chat bubble
- `toggleBubble()`: Toggle the visibility of the chat bubble
- `isBubbleVisible()`: Check if the chat bubble is visible
- `sendMessage(message)`: Send a message to the chatbot
- `resetChat()`: Reset the chat history
- `logout()`: Logout and clear all data in all stores
- `getChatHistory()`: Get the current chat history
- `setPosition(position)`: Set the position of the chatbot button
- `setWindowPosition(position)`: Set the position of the chatbot window
- `isOpen()`: Check if the chatbot is open
- `getInstance()`: Get the chatbot instance details

## Example

See the `examples/usage-example.html` file for a complete usage example.
