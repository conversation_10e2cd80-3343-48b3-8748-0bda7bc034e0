/**
 * Utility functions to manage viewport meta tag for mobile devices
 */

// Create or update the viewport meta tag to prevent zooming on mobile
export function disable<PERSON>ob<PERSON><PERSON><PERSON>() {
  let viewportMeta = document.querySelector('meta[name="viewport"]')

  if (!viewportMeta) {
    // Create a new viewport meta tag if it doesn't exist
    viewportMeta = document.createElement('meta')
    viewportMeta.setAttribute('name', 'viewport')
    document.head.appendChild(viewportMeta)
  }

  // Set viewport properties to prevent zooming
  viewportMeta.setAttribute(
    'content',
    'width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover',
  )

  // Add additional meta tags to prevent iOS text zoom
  addTextSizeAdjustMeta()
}

// Add meta tags to prevent iOS text size adjustment
function addTextSizeAdjustMeta() {
  // Remove any existing text-size-adjust meta tags
  const existingMetas = document.querySelectorAll(
    'meta[name="text-size-adjust"], meta[name="-webkit-text-size-adjust"], meta[name="-moz-text-size-adjust"], meta[name="-ms-text-size-adjust"]',
  )
  existingMetas.forEach((meta) => meta.remove())

  // Add meta tag for webkit (iOS, Safari)
  const webkitMeta = document.createElement('meta')
  webkitMeta.setAttribute('name', '-webkit-text-size-adjust')
  webkitMeta.setAttribute('content', '100%')
  document.head.appendChild(webkitMeta)

  // Add meta tag for standard
  const textSizeMeta = document.createElement('meta')
  textSizeMeta.setAttribute('name', 'text-size-adjust')
  textSizeMeta.setAttribute('content', '100%')
  document.head.appendChild(textSizeMeta)
}

// Remove text-size-adjust meta tags
function removeTextSizeAdjustMeta() {
  const metas = document.querySelectorAll(
    'meta[name="text-size-adjust"], meta[name="-webkit-text-size-adjust"], meta[name="-moz-text-size-adjust"], meta[name="-ms-text-size-adjust"]',
  )
  metas.forEach((meta) => meta.remove())
}

// Restore default viewport behavior
export function enableMobileZoom() {
  const viewportMeta = document.querySelector('meta[name="viewport"]')

  if (viewportMeta) {
    // Restore default viewport behavior
    viewportMeta.setAttribute('content', 'width=device-width, initial-scale=1.0')
  }

  // Remove text-size-adjust meta tags
  removeTextSizeAdjustMeta()
}
