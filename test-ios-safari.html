<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>iOS Safari Test - LLM RAG Chatbot</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .test-steps {
            list-style: none;
            padding: 0;
        }
        
        .test-steps li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .test-steps li:last-child {
            border-bottom: none;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        
        .status.fixed {
            background: #d4edda;
            color: #155724;
        }
        
        .status.improved {
            background: #fff3cd;
            color: #856404;
        }
        
        .button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        
        .button:hover {
            background: #0056b3;
        }
        
        .content {
            height: 200vh;
            background: linear-gradient(to bottom, #f8f9fa, #e9ecef);
            margin: 20px 0;
            padding: 20px;
            border-radius: 8px;
        }
        
        .scroll-test {
            height: 100px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 iOS Safari Chatbot Test</h1>
        
        <div class="test-section">
            <h3>🔧 Fixes Applied</h3>
            <ul class="test-steps">
                <li>✅ Added event.stopPropagation() to chat window events <span class="status fixed">FIXED</span></li>
                <li>✅ Improved touch event handling for iOS Safari <span class="status fixed">FIXED</span></li>
                <li>✅ Added debounced click-outside detection <span class="status fixed">FIXED</span></li>
                <li>✅ Enhanced body scroll locking for iOS <span class="status improved">IMPROVED</span></li>
                <li>✅ Better event listener cleanup <span class="status fixed">FIXED</span></li>
                <li>✅ Passive touch events where appropriate <span class="status improved">IMPROVED</span></li>
            </ul>
        </div>
        
        <div class="test-section">
            <h3>📱 Test Steps for iOS Safari</h3>
            <ol class="test-steps">
                <li><strong>Step 1:</strong> Tap the avatar/chat bubble to open chat window</li>
                <li><strong>Step 2:</strong> Try tapping inside the chat window - it should NOT close</li>
                <li><strong>Step 3:</strong> Try typing in the message input - should work normally</li>
                <li><strong>Step 4:</strong> Tap outside the chat window - it should close (only in non-fullscreen)</li>
                <li><strong>Step 5:</strong> Test scrolling the page behind - should work when chat is closed</li>
                <li><strong>Step 6:</strong> Test fullscreen mode on mobile - should lock body scroll</li>
                <li><strong>Step 7:</strong> Test dragging the chat bubble - should work smoothly</li>
                <li><strong>Step 8:</strong> Test rapid open/close - should not cause issues</li>
            </ol>
        </div>
        
        <div class="test-section">
            <h3>🎮 Test Controls</h3>
            <button class="button" onclick="testOpenChat()">Open Chat</button>
            <button class="button" onclick="testCloseChat()">Close Chat</button>
            <button class="button" onclick="testToggleChat()">Toggle Chat</button>
            <button class="button" onclick="testFullscreen()">Toggle Fullscreen</button>
            <button class="button" onclick="testScrollLock()">Test Scroll Lock</button>
        </div>
        
        <div class="test-section">
            <h3>📊 Expected Behavior</h3>
            <ul class="test-steps">
                <li>✅ Chat window should NOT close when tapping inside it</li>
                <li>✅ Chat window should close when tapping outside (non-fullscreen only)</li>
                <li>✅ Page scrolling should work when chat is closed</li>
                <li>✅ Body scroll should be locked in fullscreen mode</li>
                <li>✅ No rapid open/close toggling issues</li>
                <li>✅ Touch events should work smoothly</li>
                <li>✅ Dragging should work without conflicts</li>
            </ul>
        </div>
        
        <div class="scroll-test">
            <h4>Scroll Test Area</h4>
            <p>This area should be scrollable when chat is closed.</p>
            <p>Line 1</p><p>Line 2</p><p>Line 3</p><p>Line 4</p><p>Line 5</p>
            <p>Line 6</p><p>Line 7</p><p>Line 8</p><p>Line 9</p><p>Line 10</p>
        </div>
    </div>
    
    <div class="content">
        <h2>Long Content for Scroll Testing</h2>
        <p>This is a long content area to test scrolling behavior. When the chat is in fullscreen mode, this content should not be scrollable.</p>
        <p>Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.</p>
        <p>Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.</p>
        <p>Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.</p>
        <p>Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.</p>
    </div>

    <!-- Load the chatbot -->
    <script src="dist/chatbot.min.js"></script>
    
    <script>
        // Test functions
        function testOpenChat() {
            if (window.LLMRagChatbot && window.LLMRagChatbot.instance) {
                window.LLMRagChatbot.instance.open();
                console.log('Chat opened');
            }
        }
        
        function testCloseChat() {
            if (window.LLMRagChatbot && window.LLMRagChatbot.instance) {
                window.LLMRagChatbot.instance.close();
                console.log('Chat closed');
            }
        }
        
        function testToggleChat() {
            if (window.LLMRagChatbot && window.LLMRagChatbot.instance) {
                window.LLMRagChatbot.instance.toggle();
                console.log('Chat toggled');
            }
        }
        
        function testFullscreen() {
            if (window.LLMRagChatbot && window.LLMRagChatbot.instance) {
                window.LLMRagChatbot.instance.toggleFullScreen();
                console.log('Fullscreen toggled');
            }
        }
        
        function testScrollLock() {
            const body = document.body;
            if (body.style.overflow === 'hidden') {
                body.style.overflow = '';
                body.style.position = '';
                body.style.width = '';
                body.style.height = '';
                console.log('Scroll unlocked');
            } else {
                body.style.overflow = 'hidden';
                body.style.position = 'fixed';
                body.style.width = '100%';
                body.style.height = '100%';
                console.log('Scroll locked');
            }
        }
        
        // Initialize chatbot when loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking for chatbot...');
            
            // Wait a bit for the chatbot to load
            setTimeout(() => {
                if (window.LLMRagChatbot) {
                    console.log('✅ Chatbot loaded successfully');
                    
                    // Initialize with test parameters
                    window.LLMRagChatbot.init({
                        tenantId: 'test',
                        envId: 'test',
                        env: 'development'
                    });
                } else {
                    console.log('❌ Chatbot not found - make sure to build the project first');
                    alert('Chatbot not loaded. Please build the project first with: npm run build');
                }
            }, 1000);
        });
    </script>
</body>
</html>
