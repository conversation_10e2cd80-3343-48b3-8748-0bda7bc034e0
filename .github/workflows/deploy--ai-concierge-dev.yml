name: Build and deploy Node.js app to S3

on:
  push:
    branches: [develop]

env:
  NODE_VERSION: '20.x'

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    environment: 'ai-concierge-dev'
    continue-on-error: true
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node ${{ env.NODE_VERSION }} Environment
        uses: actions/setup-node@v1
        with:
          node-version: ${{ env.NODE_VERSION }}

      - name: Set env variable
        env:
          TZ: 'Asia/Tokyo'
        run: |
          echo "VITE_COMMIT=$(echo $GITHUB_SHA | cut -c 1-6)" >> $GITHUB_ENV
          echo "VITE_BUILD_DATE=$(date +'%Y%m%dT%H%M%S')" >> $GITHUB_ENV
      - name: 'Build Project Dependencies Using Npm'
        shell: bash
        env:
          VITE_RAG_SERVICE_BASE_URL: ${{ vars.VITE_RAG_SERVICE_BASE_URL }}
          VITE_VERSION: ${{ vars.VITE_VERSION }}
        run: |
          rm -f .eslintrc
          npm install --force
          export VITE_VERSION=$VITE_VERSION.$VITE_COMMIT.$VITE_BUILD_DATE
          npm run build

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.PNL_AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.PNL_AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_REGION }}
          role-to-assume: arn:aws:iam::376129839452:role/admin-users-ai-concierge-dev
          role-duration-seconds: 7200
      - name: s3 sync to bucket
        run: |
          aws s3 sync "${SOURCE_DIR}" "s3://${AWS_S3_BUCKET}" \
            --no-progress \
            --follow-symlinks
        env:
          AWS_S3_BUCKET: ${{ secrets.AWS_S3_BUCKET }}
          SOURCE_DIR: 'dist'
