<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>LLMRagChatbot Usage Example</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 20px;
      }
      button {
        margin: 5px;
        padding: 8px 12px;
        background-color: #4caf50;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
      }
      button:hover {
        background-color: #45a049;
      }
      pre {
        background-color: #f5f5f5;
        padding: 10px;
        border-radius: 4px;
        overflow: auto;
      }
      .control-panel {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      h2 {
        margin-top: 0;
      }
    </style>
  </head>
  <body>
    <h1>LLMRagChatbot Usage Example</h1>

    <div class="control-panel">
      <h2>Default Instance Controls</h2>
      <button onclick="toggleChatbot()">Toggle Chatbot</button>
      <button onclick="openChatbot()">Open Chatbot</button>
      <button onclick="closeChatbot()">Close Chatbot</button>
      <button onclick="sendMessage()">Send Test Message</button>
      <button onclick="resetChat()">Reset Chat</button>
      <button onclick="getChatHistory()">Get Chat History</button>

      <h3>Bubble Visibility</h3>
      <button onclick="hideBubble()">Hide Bubble</button>
      <button onclick="showBubble()">Show Bubble</button>
      <button onclick="toggleBubble()">Toggle Bubble</button>
      <button onclick="checkBubbleVisibility()">Check Bubble Visibility</button>
    </div>

    <div class="control-panel">
      <h2>Admin Controls</h2>
      <div>
        <label for="tenantId">Tenant ID:</label>
        <input type="text" id="tenantId" placeholder="Enter tenant ID" />
      </div>
      <div>
        <label for="envId">Environment ID:</label>
        <input type="text" id="envId" placeholder="Enter environment ID" />
      </div>
      <div>
        <label for="env">Environment:</label>
        <input type="text" id="env" placeholder="Enter environment" />
      </div>
      <div>
        <label for="token">Token:</label>
        <input type="text" id="token" placeholder="Enter token" />
      </div>
      <div>
        <label for="refresh_token">Refresh Token:</label>
        <input type="text" id="refresh_token" placeholder="Enter refresh token" />
      </div>
      <div>
        <label for="liffId">LIFF ID:</label>
        <input type="text" id="liffId" placeholder="Enter LIFF ID" />
      </div>
      <div>
        <button onclick="reloadChatbot()">Reload Chatbot</button>
        <button onclick="updateAuthOnly()">Update Auth Only</button>
      </div>
    </div>

    <div class="control-panel">
      <h2>Create New Instance</h2>
      <button onclick="createNewInstance()">Create New Instance</button>
      <div id="new-instance-controls" style="display: none">
        <h3>New Instance Controls</h3>
        <button onclick="toggleNewInstance()">Toggle New Instance</button>
        <button onclick="openNewInstance()">Open New Instance</button>
        <button onclick="closeNewInstance()">Close New Instance</button>
      </div>
    </div>

    <div class="control-panel">
      <h2>Results</h2>
      <pre id="results">No results yet</pre>
    </div>

    <!-- Include the chatbot script -->
    <script src="../dist/chatbot.min.js"></script>

    <script>
      // Reference to the results element
      const resultsElement = document.getElementById('results')

      // Reference to the default instance
      const chatbot = window.LLMRagChatbot.instance

      // Variable to store a new instance
      let newChatbotInstance = null

      // Function to display results
      function displayResult(message) {
        resultsElement.textContent = message
      }

      // Default instance functions
      function toggleChatbot() {
        chatbot.toggle()
        displayResult('Toggled default chatbot instance')
      }

      function openChatbot() {
        chatbot.open()
        displayResult('Opened default chatbot instance')
      }

      function closeChatbot() {
        chatbot.close()
        displayResult('Closed default chatbot instance')
      }

      async function sendMessage() {
        await chatbot.sendMessage('Hello, this is a test message!')
        displayResult('Sent test message to default chatbot instance')
      }

      function resetChat() {
        chatbot.resetChat()
        displayResult('Reset chat history for default chatbot instance')
      }

      function getChatHistory() {
        const history = chatbot.getChatHistory()
        displayResult('Chat History: ' + JSON.stringify(history, null, 2))
      }

      // New instance functions
      function createNewInstance() {
        // Get LIFF ID value
        const liffId = document.getElementById('liffId').value || undefined

        // Create a new instance with custom options
        newChatbotInstance = new window.LLMRagChatbot.Class({
          position: { left: '20px', top: '20px' },
          windowPosition: { left: '20px' },
          liffId: liffId,
        })

        // Show the new instance controls
        document.getElementById('new-instance-controls').style.display = 'block'

        displayResult('Created new chatbot instance with custom position')
      }

      function toggleNewInstance() {
        if (newChatbotInstance) {
          newChatbotInstance.toggle()
          displayResult('Toggled new chatbot instance')
        } else {
          displayResult('New chatbot instance not created yet')
        }
      }

      function openNewInstance() {
        if (newChatbotInstance) {
          newChatbotInstance.open()
          displayResult('Opened new chatbot instance')
        } else {
          displayResult('New chatbot instance not created yet')
        }
      }

      function closeNewInstance() {
        if (newChatbotInstance) {
          newChatbotInstance.close()
          displayResult('Closed new chatbot instance')
        } else {
          displayResult('New chatbot instance not created yet')
        }
      }

      // Admin control functions
      function getAuthParams() {
        return {
          tenantId: document.getElementById('tenantId').value || undefined,
          envId: document.getElementById('envId').value || undefined,
          env: document.getElementById('env').value || undefined,
          token: document.getElementById('token').value || undefined,
          refresh_token: document.getElementById('refresh_token').value || undefined,
          liffId: document.getElementById('liffId').value || undefined,
        }
      }

      function reloadChatbot() {
        const params = getAuthParams()
        chatbot.reload(params)
        displayResult('Reloaded chatbot with new parameters: ' + JSON.stringify(params, null, 2))
      }

      function updateAuthOnly() {
        const params = getAuthParams()
        chatbot.updateAuth(params)
        displayResult(
          'Updated auth parameters without resetting chat: ' + JSON.stringify(params, null, 2),
        )
      }

      // Bubble visibility functions
      function hideBubble() {
        chatbot.hideBubble()
        displayResult('Chat bubble hidden')
      }

      function showBubble() {
        chatbot.showBubble()
        displayResult('Chat bubble shown')
      }

      function toggleBubble() {
        chatbot.toggleBubble()
        displayResult('Chat bubble visibility toggled')
      }

      function checkBubbleVisibility() {
        const isVisible = chatbot.isBubbleVisible()
        displayResult('Chat bubble is ' + (isVisible ? 'visible' : 'hidden'))
      }
    </script>
  </body>
</html>
