<template>
  <section
    class="aiko-flex aiko-flex-col aiko-items-center aiko-justify-start aiko-pt-4 aiko-h-full"
  >
    <div
      class="aiko-flex aiko-flex-col aiko-items-center aiko-justify-center aiko-pr-4 aiko-mx-auto lg:py-4 aiko-w-full"
    >
      <div class="aiko-w-full md:mt-0 sm:max-w-md xl:p-0">
        <div class="aiko-p-6 space-y-3 md:space-y-6 sm:p-8">
          <h1 class="aiko-text-base aiko-text-gray-900 dark:text-white aiko-text-center">
            {{ message }}
          </h1>
          <div class="aiko-flex aiko-flex-row aiko-gap-2 aiko-items-center">
            <button
              type="submit"
              class="aiko-w-full aiko-text-xs aiko-text-white aiko-focus:ring-4 aiko-focus:outline-none aiko-font-medium aiko-rounded-lg aiko-px-2 aiko-py-2 aiko-text-center"
              :class="[`aiko-bg-${color_primary}-500`]"
              @click="onConfirm"
            >
              <span> チャットをクリアする </span>
            </button>
            <button
              type="submit"
              class="aiko-w-full aiko-text-xs aiko-text-gray-900 aiko-bg-gray-200 aiko-hover:bg-gray-700 aiko-focus:ring-4 aiko-focus:outline-none aiko-focus:ring-gray-300 aiko-font-medium aiko-rounded-lg aiko-px-2 aiko-py-2 aiko-text-center dark:bg-gray-600 dark:hover:bg-gray-700 dark:focus:ring-gray-800"
              @click="onCancel"
            >
              <span> キャンセル </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/settings'
import { storeToRefs } from 'pinia'

const settingsStore = useSettingsStore()
const { color_primary } = storeToRefs(settingsStore)

const props = defineProps({
  title: {
    type: String,
    default: '確認',
  },
  message: {
    type: String,
    default: '現在のチャットをクリアしますか？',
  },
})

const emit = defineEmits(['onConfirm', 'onCancel'])
const onConfirm = () => {
  emit('onConfirm')
}

const onCancel = () => {
  emit('onCancel')
}
</script>
