<template>
  <div
    class="error-container aiko-flex aiko-flex-col aiko-items-center aiko-justify-center aiko-h-full aiko-p-4"
  >
    <div class="error-icon aiko-mb-4">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="64"
        height="64"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="aiko-text-red-500"
      >
        <circle cx="12" cy="12" r="10"></circle>
        <line x1="12" y1="8" x2="12" y2="12"></line>
        <line x1="12" y1="16" x2="12.01" y2="16"></line>
      </svg>
    </div>
    <h3 class="aiko-text-lg aiko-font-medium aiko-text-red-600 dark:text-red-400 aiko-mb-2">
      {{ title }}
    </h3>
    <p class="aiko-text-sm aiko-text-gray-600 dark:text-gray-300 aiko-text-center aiko-mb-4">
      {{ message }}
    </p>
    <div class="aiko-flex aiko-gap-2 aiko-mt-4">
      <button
        @click="retry"
        class="aiko-px-4 aiko-py-2 aiko-bg-red-500 aiko-text-white aiko-rounded-md aiko-hover:bg-red-600 aiko-transition-colors"
      >
        リトライ
      </button>
      <button
        @click="closeChat"
        class="aiko-px-4 aiko-py-2 aiko-bg-gray-500 aiko-text-black dark:text-white aiko-rounded-md aiko-hover:bg-gray-600 aiko-transition-colors"
      >
        閉じる
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAuthStore } from '@/stores/auth'
import { useChatStore } from '@/stores/chat'
import { useSettingsStore } from '@/stores/settings'

const props = defineProps({
  title: {
    type: String,
    default: 'Connection Error',
  },
  message: {
    type: String,
    default: 'Unable to connect to the chat service. Please check your connection and try again.',
  },
})

const authStore = useAuthStore()
const chatStore = useChatStore()
const settingsStore = useSettingsStore()

const retry = async () => {
  await authStore.guestLogin()
  await chatStore.chatInit()
}

const closeChat = () => {
  settingsStore.isOpen = false
}
</script>

<style scoped>
.error-container {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-icon {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
</style>
