import './assets/custom.scss'
import { Icon } from '@iconify/vue'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import { createRouter, createWebHistory } from 'vue-router'
import routes from './routes'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const app = createApp(App)
const router = createRouter({
  history: createWebHistory(),
  routes,
})
const div = document.createElement('div')
div.id = 'llm-rag-chatbot'
document.body.appendChild(div)
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)
app.use(router)
app.use(pinia)
app.component('Icon', Icon)

app.mount(div)
