<template>
  <div
    ref="messageListRef"
    class="aiko-flex aiko-flex-col aiko-gap-4 aiko-overflow-y-auto aiko-h-full aiko-pt-4 aiko-px-4 aiko-scrollbar-thin message-list-transparent"
    :style="{
      paddingBottom: isFullScreen ? '80px' : '70px',
      marginBottom: isFullScreen ? '0' : '0',
    }"
  >
    <ChatBotMessage
      v-for="(chat, index) in chats"
      :key="index"
      v-bind="chat"
      :right="chat.type === 'human'"
      :sender-avatar="chat.type === 'ai' && avatar_url"
      :sender-name="chat.type === 'ai' ? chatbot_name : ''"
    />
    <ChatBotMessage
      v-if="loadings['chat']"
      :loading="loadings['chat']"
      :sender-avatar="avatar_url"
      :sender-name="chatbot_name"
    />
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/settings'
import { ref, watch } from 'vue'
import { storeToRefs } from 'pinia'
import ChatBotMessage from '@/components/ChatBotMessage.vue'
import { useChatStore } from '@/stores/chat'

const chatStore = useChatStore()
const { messageListRef, loadings } = storeToRefs(chatStore)
const settingsStore = useSettingsStore()
const { avatar_url, chatbot_name, isFullScreen } = storeToRefs(settingsStore)
const props = defineProps<{
  chats: any[]
}>()

//watch chats then scroll to bottom
watch(
  () => props.chats,
  () => {
    chatStore.scrollToBottom()
  },
  { immediate: true, deep: true },
)
</script>

<style>
.message-list-transparent {
  background-color: transparent !important;
}

/* Dark mode styles for message list */
.dark .message-list-transparent {
  background-color: transparent !important;
  color: #f0f0f0;
}

/* Scrollbar styling for dark mode */
.dark .scrollbar-thin::-webkit-scrollbar-track {
  background: rgba(30, 30, 30, 0.5);
}

.dark .scrollbar-thin::-webkit-scrollbar-thumb {
  background-color: rgba(100, 100, 100, 0.5);
  border-radius: 6px;
}
</style>
