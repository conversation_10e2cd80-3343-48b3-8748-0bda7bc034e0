import { defineStore } from 'pinia'
import { useAuthStore } from '@/stores/auth'
import { useAPI } from '@/composables/useAPI'
import { useChatStore } from '@/stores/chat'

export const useSurveyStore = defineStore('surveyStore', {
  state: () => ({
    loadings: {} as Record<string, boolean>,
    errors: {} as Record<string, any>,
  }),
  getters: {},
  actions: {
    async submitSurvey(session_id: string | null, chat_id: string, survey: any) {
      const authStore = useAuthStore()
      const chatStore = useChatStore()
      try {
        chatStore.markSurveyDone(chat_id)
        chatStore.addMessage(survey?.text, {
          type: 'human',
        })
        const { envId, env } = authStore
        this.loadings.chat = true
        this.errors.chat = null

        let apiUrl = '/v1/survey'
        if (env) {
          apiUrl = '/v1/survey/' + env
        } else if (envId) {
          apiUrl = '/v1/survey/env/' + envId
        }
        const response = await useAPI().ragService.post(apiUrl, {
          session_id,
          chat_id,
          score: survey.value,
        })
        if (response.data) {
          chatStore.addMessage(survey?.reply_message)
        }
        return true
      } catch (error: any) {
        console.log('🚀 ~ chat ~ error:', error)
        this.errors.chat = error?.response?.data || error
        return false
      } finally {
        this.loadings.chat = false
      }
    },
  },
})
