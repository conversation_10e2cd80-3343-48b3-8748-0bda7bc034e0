#llm-rag-chatbot {
    @import url("https://fonts.aiko-googleapis.com/css2?family=Noto+Sans+JP:wght@100..900&display=swap");
    font-family: "Noto Sans JP", sans-serif;

    /* CSS variables for color transparency */
    --color-primary-rgb: 14, 165, 233; /* Default sky-500 RGB values */

    /* Prevent iOS zoom on inputs */
    -webkit-text-size-adjust: 100%;
    text-size-adjust: 100%;

    /* Ensure inputs have minimum font size to prevent iOS zoom */
    input,
    textarea,
    select {
        font-size: 16px !important; /* Minimum font size to prevent iOS zoom */
    }

    /* Specific class to prevent zoom */
    .aiko-no-zoom {
        font-size: 16px !important;
    }

    /* Set RGB values for different primary colors */
    .aiko-bg-sky-600 {
        --color-primary-rgb: 2, 132, 199;
    }
    .aiko-bg-blue-600 {
        --color-primary-rgb: 37, 99, 235;
    }
    .aiko-bg-indigo-600 {
        --color-primary-rgb: 79, 70, 229;
    }
    .aiko-bg-purple-600 {
        --color-primary-rgb: 147, 51, 234;
    }
    .aiko-bg-pink-600 {
        --color-primary-rgb: 219, 39, 119;
    }
    .aiko-bg-rose-600 {
        --color-primary-rgb: 225, 29, 72;
    }
    .aiko-bg-red-600 {
        --color-primary-rgb: 220, 38, 38;
    }
    .aiko-bg-orange-600 {
        --color-primary-rgb: 234, 88, 12;
    }
    .aiko-bg-amber-600 {
        --color-primary-rgb: 217, 119, 6;
    }
    .aiko-bg-yellow-600 {
        --color-primary-rgb: 202, 138, 4;
    }
    .aiko-bg-lime-600 {
        --color-primary-rgb: 101, 163, 13;
    }
    .aiko-bg-green-600 {
        --color-primary-rgb: 22, 163, 74;
    }
    .aiko-bg-emerald-600 {
        --color-primary-rgb: 5, 150, 105;
    }
    .aiko-bg-teal-600 {
        --color-primary-rgb: 13, 148, 136;
    }
    .aiko-bg-cyan-600 {
        --color-primary-rgb: 8, 145, 178;
    }
    .aiko-bg-gray-600 {
        --color-primary-rgb: 75, 85, 99;
    }
    .aiko-bg-customone-600 {
        --color-primary-rgb: 0, 145, 67;
    }

    .aiko-scrollbar-thin {
        scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        scrollbar-width: thin;
    }

    .aiko-scrollbar-thin::-webkit-scrollbar-track,
    body::-webkit-scrollbar-track {
        border-radius: 25px !important;
        cursor: pointer !important;
        background: transparent !important;
    }

    .aiko-scrollbar-thin::-webkit-scrollbar-thumb,
    body::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.1) !important;
        border-radius: 25px !important;
    }

    /* Layout */
    .aiko-flex {
        display: flex;
    }

    .aiko-flex-col {
        flex-direction: column;
    }

    .aiko-flex-row {
        flex-direction: row;
    }

    .aiko-flex-row-reverse {
        flex-direction: row-reverse;
    }

    .aiko-flex-wrap {
        flex-wrap: wrap;
    }

    .aiko-items-start {
        align-items: flex-start;
    }

    .aiko-items-center {
        align-items: center;
    }

    .aiko-items-end {
        align-items: flex-end;
    }

    .aiko-items-baseline {
        align-items: baseline;
    }

    .aiko-justify-start {
        justify-content: flex-start;
    }

    .aiko-justify-center {
        justify-content: center;
    }

    .aiko-justify-end {
        justify-content: flex-end;
    }

    .aiko-gap-1 {
        gap: 0.25rem;
    }

    .aiko-gap-2 {
        gap: 0.5rem;
    }

    .aiko-gap-2\.5 {
        gap: 0.625rem;
    }

    .aiko-gap-3 {
        gap: 0.75rem;
    }

    .aiko-gap-4 {
        gap: 1rem;
    }

    .aiko-self-end {
        align-self: flex-end;
    }

    /* Width and Height */
    .aiko-w-full {
        width: 100%;
    }

    .aiko-w-fit {
        width: fit-content;
    }

    .aiko-w-8 {
        width: 2rem;
    }

    .aiko-w-12 {
        width: 3rem;
    }

    .aiko-top-2 {
        top: 0.5rem;
    }

    /* Group selector for hover effects */
    .aiko-group {
        display: flex;
    }

    .aiko-w-20 {
        width: 5rem;
    }

    .aiko-h-full {
        height: 100%;
    }

    .aiko-h-8 {
        height: 2rem;
    }

    .aiko-h-12 {
        height: 3rem;
    }

    .aiko-h-0 {
        height: 0;
    }

    .aiko-h-screen {
        height: 100vh;
    }

    .aiko-max-w-\[80\%\] {
        max-width: 80%;
    }

    .aiko-max-h-20 {
        max-height: 5rem;
    }

    .aiko-min-h-10 {
        min-height: 2.5rem;
    }

    /* Spacing */
    .aiko-p-0 {
        padding: 0;
    }

    .aiko-p-0\.5 {
        padding: 0.125rem;
    }

    .aiko-p-2 {
        padding: 0.5rem;
    }

    .aiko-p-2\.5 {
        padding: 0.625rem;
    }

    .aiko-p-6 {
        padding: 1.5rem;
    }

    .aiko-px-2 {
        padding-left: 0.5rem;
        padding-right: 0.5rem;
    }

    .aiko-px-3 {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }

    .aiko-px-4 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .aiko-px-5 {
        padding-left: 1.25rem;
        padding-right: 1.25rem;
    }

    .aiko-py-1 {
        padding-top: 0.25rem;
        padding-bottom: 0.25rem;
    }

    .aiko-py-2 {
        padding-top: 0.5rem;
        padding-bottom: 0.5rem;
    }

    .aiko-py-3 {
        padding-top: 0.75rem;
        padding-bottom: 0.75rem;
    }

    .aiko-py-4 {
        padding-top: 1rem;
        padding-bottom: 1rem;
    }

    .aiko-pt-4 {
        padding-top: 1rem;
    }

    .aiko-pb-2 {
        padding-bottom: 0.5rem;
    }

    .aiko-pb-32 {
        padding-bottom: 8rem;
    }

    .aiko-pr-2 {
        padding-right: 0.5rem;
    }

    .aiko-m-0 {
        margin: 0;
    }

    .aiko-mx-2 {
        margin-left: 0.5rem;
        margin-right: 0.5rem;
    }

    .aiko-mx-auto {
        margin-left: auto;
        margin-right: auto;
    }

    .aiko-my-0 {
        margin-top: 0;
        margin-bottom: 0;
    }

    .aiko-mb-0 {
        margin-bottom: 0;
    }

    .aiko-mb-2 {
        margin-bottom: 0.5rem;
    }

    .aiko-mb-\[60px\] {
        margin-bottom: 60px;
    }

    .aiko-mt-0 {
        margin-top: 0;
    }

    .aiko-mt-4 {
        margin-top: 1rem;
    }

    .aiko--mt-0 {
        margin-top: 0;
    }

    /* Typography */
    .aiko-text-xs {
        font-size: 0.75rem;
    }

    .aiko-text-sm {
        font-size: 0.875rem;
    }

    .aiko-text-base {
        font-size: 1rem;
    }

    .aiko-text-2xl {
        font-size: 1.5rem;
    }

    .aiko-text-4xl {
        font-size: 2.25rem;
    }

    .aiko-h-8 {
        height: 2rem;
    }

    .aiko-w-8 {
        width: 2rem;
    }

    .aiko-font-normal {
        font-weight: 400;
    }

    .aiko-font-medium {
        font-weight: 500;
    }

    .aiko-font-semibold {
        font-weight: 600;
    }

    .aiko-font-bold {
        font-weight: 700;
    }

    .aiko-text-center {
        text-align: center;
    }

    .aiko-leading-1\.5 {
        line-height: 1.375;
    }

    .aiko-leading-tight {
        line-height: 1.25;
    }

    .aiko-tracking-tight {
        letter-spacing: -0.025em;
    }

    .aiko-whitespace-pre-wrap {
        white-space: pre-wrap;
    }

    .aiko-break-words {
        word-break: break-word;
    }

    .aiko-select-none {
        user-select: none;
    }

    /* Colors */
    .aiko-text-white {
        color: white;
    }

    .aiko-text-gray-400 {
        color: #9ca3af;
    }

    .aiko-text-gray-500 {
        color: #6b7280;
    }

    .aiko-text-gray-900 {
        color: #111827;
    }

    .aiko-text-blue-500 {
        color: #3b82f6;
    }

    .aiko-bg-white {
        background-color: white;
    }

    .aiko-bg-gray-50 {
        background-color: #f9fafb;
    }

    .aiko-bg-gray-100 {
        background-color: #f3f4f6;
    }

    .aiko-bg-gray-200 {
        background-color: #e5e7eb;
    }

    .aiko-bg-gray-700 {
        background-color: #374151;
    }

    .aiko-bg-gray-800 {
        background-color: #1f2937;
    }

    .aiko-border-gray-200 {
        border-color: #e5e7eb;
    }

    .aiko-border-gray-300 {
        border-color: #d1d5db;
    }

    .aiko-border-gray-600 {
        border-color: #4b5563;
    }

    .aiko-border-gray-700 {
        border-color: #374151;
    }

    /* Tailwind color variations */

    /* Sky (default primary) */
    .aiko-bg-sky-100 {
        background-color: #e0f2fe;
    }

    .aiko-bg-sky-200 {
        background-color: #bae6fd;
    }

    .aiko-bg-sky-300 {
        background-color: #7dd3fc;
    }

    .aiko-bg-sky-400 {
        background-color: #38bdf8;
    }

    .aiko-bg-sky-500 {
        background-color: #0ea5e9;
    }

    .aiko-bg-sky-600 {
        background-color: #0284c7;
    }

    .aiko-bg-sky-700 {
        background-color: #0369a1;
    }

    .aiko-bg-sky-800 {
        background-color: #075985;
    }

    .aiko-bg-sky-900 {
        background-color: #0c4a6e;
    }

    .aiko-border-sky-200 {
        border-color: #bae6fd;
    }

    .aiko-border-sky-300 {
        border-color: #7dd3fc;
    }

    .aiko-text-sky-600 {
        color: #0284c7;
    }

    .aiko-focus\:ring-sky-300:focus {
        box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.2);
    }

    .aiko-focus\:ring-sky-600:focus {
        box-shadow: 0 0 0 3px rgba(2, 132, 199, 0.4);
    }

    .aiko-focus\:border-sky-600:focus {
        border-color: #0284c7;
    }

    .aiko-hover\:bg-sky-700:hover {
        background-color: #0369a1;
    }

    .aiko-hover\:bg-sky-800:hover {
        background-color: #075985;
    }

    .aiko-hover\:border-sky-300:hover {
        border-color: #7dd3fc;
    }

    /* Blue */
    .aiko-bg-blue-100 {
        background-color: #dbeafe;
    }

    .aiko-bg-blue-200 {
        background-color: #bfdbfe;
    }

    .aiko-bg-blue-300 {
        background-color: #93c5fd;
    }

    .aiko-bg-blue-400 {
        background-color: #60a5fa;
    }

    .aiko-bg-blue-500 {
        background-color: #3b82f6;
    }

    .aiko-bg-blue-600 {
        background-color: #2563eb;
    }

    .aiko-bg-blue-700 {
        background-color: #1d4ed8;
    }

    .aiko-bg-blue-800 {
        background-color: #1e40af;
    }

    .aiko-bg-blue-900 {
        background-color: #1e3a8a;
    }

    .aiko-border-blue-200 {
        border-color: #bfdbfe;
    }

    .aiko-border-blue-300 {
        border-color: #93c5fd;
    }

    .aiko-text-blue-600 {
        color: #2563eb;
    }

    .aiko-focus\:ring-blue-300:focus {
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
    }

    .aiko-focus\:ring-blue-600:focus {
        box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.4);
    }

    .aiko-focus\:border-blue-600:focus {
        border-color: #2563eb;
    }

    .aiko-hover\:bg-blue-700:hover {
        background-color: #1d4ed8;
    }

    .aiko-hover\:bg-blue-800:hover {
        background-color: #1e40af;
    }

    .aiko-hover\:border-blue-300:hover {
        border-color: #93c5fd;
    }

    /* Red */
    .aiko-bg-red-100 {
        background-color: #fee2e2;
    }

    .aiko-bg-red-200 {
        background-color: #fecaca;
    }

    .aiko-bg-red-300 {
        background-color: #fca5a5;
    }

    .aiko-bg-red-400 {
        background-color: #f87171;
    }

    .aiko-bg-red-500 {
        background-color: #ef4444;
    }

    .aiko-bg-red-600 {
        background-color: #dc2626;
    }

    .aiko-bg-red-700 {
        background-color: #b91c1c;
    }

    .aiko-bg-red-800 {
        background-color: #991b1b;
    }

    .aiko-bg-red-900 {
        background-color: #7f1d1d;
    }

    .aiko-border-red-200 {
        border-color: #fecaca;
    }

    .aiko-border-red-300 {
        border-color: #fca5a5;
    }

    .aiko-text-red-600 {
        color: #dc2626;
    }

    .aiko-focus\:ring-red-300:focus {
        box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
    }

    .aiko-focus\:ring-red-600:focus {
        box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.4);
    }

    .aiko-focus\:border-red-600:focus {
        border-color: #dc2626;
    }

    .aiko-hover\:bg-red-700:hover {
        background-color: #b91c1c;
    }

    .aiko-hover\:bg-red-800:hover {
        background-color: #991b1b;
    }

    .aiko-hover\:border-red-300:hover {
        border-color: #fca5a5;
    }

    /* Green */
    .aiko-bg-green-100 {
        background-color: #dcfce7;
    }

    .aiko-bg-green-200 {
        background-color: #bbf7d0;
    }

    .aiko-bg-green-300 {
        background-color: #86efac;
    }

    .aiko-bg-green-400 {
        background-color: #4ade80;
    }

    .aiko-bg-green-500 {
        background-color: #22c55e;
    }

    .aiko-bg-green-600 {
        background-color: #16a34a;
    }

    .aiko-bg-green-700 {
        background-color: #15803d;
    }

    .aiko-bg-green-800 {
        background-color: #166534;
    }

    .aiko-bg-green-900 {
        background-color: #14532d;
    }

    .aiko-border-green-200 {
        border-color: #bbf7d0;
    }

    .aiko-border-green-300 {
        border-color: #86efac;
    }

    .aiko-text-green-600 {
        color: #16a34a;
    }

    .aiko-focus\:ring-green-300:focus {
        box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.2);
    }

    .aiko-focus\:ring-green-600:focus {
        box-shadow: 0 0 0 3px rgba(22, 163, 74, 0.4);
    }

    .aiko-focus\:border-green-600:focus {
        border-color: #16a34a;
    }

    .aiko-hover\:bg-green-700:hover {
        background-color: #15803d;
    }

    .aiko-hover\:bg-green-800:hover {
        background-color: #166534;
    }

    .aiko-hover\:border-green-300:hover {
        border-color: #86efac;
    }

    /* Yellow */
    .aiko-bg-yellow-100 {
        background-color: #fef9c3;
    }

    .aiko-bg-yellow-200 {
        background-color: #fef08a;
    }

    .aiko-bg-yellow-300 {
        background-color: #fde047;
    }

    .aiko-bg-yellow-400 {
        background-color: #facc15;
    }

    .aiko-bg-yellow-500 {
        background-color: #eab308;
    }

    .aiko-bg-yellow-600 {
        background-color: #ca8a04;
    }

    .aiko-bg-yellow-700 {
        background-color: #a16207;
    }

    .aiko-bg-yellow-800 {
        background-color: #854d0e;
    }

    .aiko-bg-yellow-900 {
        background-color: #713f12;
    }

    .aiko-border-yellow-200 {
        border-color: #fef08a;
    }

    .aiko-border-yellow-300 {
        border-color: #fde047;
    }

    .aiko-text-yellow-600 {
        color: #ca8a04;
    }

    .aiko-focus\:ring-yellow-300:focus {
        box-shadow: 0 0 0 3px rgba(234, 179, 8, 0.2);
    }

    .aiko-focus\:ring-yellow-600:focus {
        box-shadow: 0 0 0 3px rgba(202, 138, 4, 0.4);
    }

    .aiko-focus\:border-yellow-600:focus {
        border-color: #ca8a04;
    }

    .aiko-hover\:bg-yellow-700:hover {
        background-color: #a16207;
    }

    .aiko-hover\:bg-yellow-800:hover {
        background-color: #854d0e;
    }

    .aiko-hover\:border-yellow-300:hover {
        border-color: #fde047;
    }

    /* Purple */
    .aiko-bg-purple-100 {
        background-color: #f3e8ff;
    }

    .aiko-bg-purple-200 {
        background-color: #e9d5ff;
    }

    .aiko-bg-purple-300 {
        background-color: #d8b4fe;
    }

    .aiko-bg-purple-400 {
        background-color: #c084fc;
    }

    .aiko-bg-purple-500 {
        background-color: #a855f7;
    }

    .aiko-bg-purple-600 {
        background-color: #9333ea;
    }

    .aiko-bg-purple-700 {
        background-color: #7e22ce;
    }

    .aiko-bg-purple-800 {
        background-color: #6b21a8;
    }

    .aiko-bg-purple-900 {
        background-color: #581c87;
    }

    .aiko-border-purple-200 {
        border-color: #e9d5ff;
    }

    .aiko-border-purple-300 {
        border-color: #d8b4fe;
    }

    .aiko-text-purple-600 {
        color: #9333ea;
    }

    .aiko-focus\:ring-purple-300:focus {
        box-shadow: 0 0 0 3px rgba(168, 85, 247, 0.2);
    }

    .aiko-focus\:ring-purple-600:focus {
        box-shadow: 0 0 0 3px rgba(147, 51, 234, 0.4);
    }

    .aiko-focus\:border-purple-600:focus {
        border-color: #9333ea;
    }

    .aiko-hover\:bg-purple-700:hover {
        background-color: #7e22ce;
    }

    .aiko-hover\:bg-purple-800:hover {
        background-color: #6b21a8;
    }

    .aiko-hover\:border-purple-300:hover {
        border-color: #d8b4fe;
    }

    /* Pink */
    .aiko-bg-pink-100 {
        background-color: #fce7f3;
    }

    .aiko-bg-pink-200 {
        background-color: #fbcfe8;
    }

    .aiko-bg-pink-300 {
        background-color: #f9a8d4;
    }

    .aiko-bg-pink-400 {
        background-color: #f472b6;
    }

    .aiko-bg-pink-500 {
        background-color: #ec4899;
    }

    .aiko-bg-pink-600 {
        background-color: #db2777;
    }

    .aiko-bg-pink-700 {
        background-color: #be185d;
    }

    .aiko-bg-pink-800 {
        background-color: #9d174d;
    }

    .aiko-bg-pink-900 {
        background-color: #831843;
    }

    .aiko-border-pink-200 {
        border-color: #fbcfe8;
    }

    .aiko-border-pink-300 {
        border-color: #f9a8d4;
    }

    .aiko-text-pink-600 {
        color: #db2777;
    }

    .aiko-focus\:ring-pink-300:focus {
        box-shadow: 0 0 0 3px rgba(236, 72, 153, 0.2);
    }

    .aiko-focus\:ring-pink-600:focus {
        box-shadow: 0 0 0 3px rgba(219, 39, 119, 0.4);
    }

    .aiko-focus\:border-pink-600:focus {
        border-color: #db2777;
    }

    .aiko-hover\:bg-pink-700:hover {
        background-color: #be185d;
    }

    .aiko-hover\:bg-pink-800:hover {
        background-color: #9d174d;
    }

    .aiko-hover\:border-pink-300:hover {
        border-color: #f9a8d4;
    }

    /* Rose */
    .aiko-bg-rose-100 {
        background-color: #ffe4e6;
    }

    .aiko-bg-rose-200 {
        background-color: #fecdd3;
    }

    .aiko-bg-rose-300 {
        background-color: #fda4af;
    }

    .aiko-bg-rose-400 {
        background-color: #fb7185;
    }

    .aiko-bg-rose-500 {
        background-color: #f43f5e;
    }

    .aiko-bg-rose-600 {
        background-color: #e11d48;
    }

    .aiko-bg-rose-700 {
        background-color: #be123c;
    }

    .aiko-bg-rose-800 {
        background-color: #9f1239;
    }

    .aiko-bg-rose-900 {
        background-color: #881337;
    }

    .aiko-border-rose-200 {
        border-color: #fecdd3;
    }

    .aiko-border-rose-300 {
        border-color: #fda4af;
    }

    .aiko-text-rose-600 {
        color: #e11d48;
    }

    .aiko-focus\:ring-rose-300:focus {
        box-shadow: 0 0 0 3px rgba(244, 63, 94, 0.2);
    }

    .aiko-focus\:ring-rose-600:focus {
        box-shadow: 0 0 0 3px rgba(225, 29, 72, 0.4);
    }

    .aiko-focus\:border-rose-600:focus {
        border-color: #e11d48;
    }

    .aiko-hover\:bg-rose-700:hover {
        background-color: #be123c;
    }

    .aiko-hover\:bg-rose-800:hover {
        background-color: #9f1239;
    }

    .aiko-hover\:border-rose-300:hover {
        border-color: #fda4af;
    }

    /* Amber */
    .aiko-bg-amber-100 {
        background-color: #fef3c7;
    }

    .aiko-bg-amber-200 {
        background-color: #fde68a;
    }

    .aiko-bg-amber-300 {
        background-color: #fcd34d;
    }

    .aiko-bg-amber-400 {
        background-color: #fbbf24;
    }

    .aiko-bg-amber-500 {
        background-color: #f59e0b;
    }

    .aiko-bg-amber-600 {
        background-color: #d97706;
    }

    .aiko-bg-amber-700 {
        background-color: #b45309;
    }

    .aiko-bg-amber-800 {
        background-color: #92400e;
    }

    .aiko-bg-amber-900 {
        background-color: #78350f;
    }

    .aiko-border-amber-200 {
        border-color: #fde68a;
    }

    .aiko-border-amber-300 {
        border-color: #fcd34d;
    }

    .aiko-text-amber-600 {
        color: #d97706;
    }

    .aiko-focus\:ring-amber-300:focus {
        box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
    }

    .aiko-focus\:ring-amber-600:focus {
        box-shadow: 0 0 0 3px rgba(217, 119, 6, 0.4);
    }

    .aiko-focus\:border-amber-600:focus {
        border-color: #d97706;
    }

    .aiko-hover\:bg-amber-700:hover {
        background-color: #b45309;
    }

    .aiko-hover\:bg-amber-800:hover {
        background-color: #92400e;
    }

    .aiko-hover\:border-amber-300:hover {
        border-color: #fcd34d;
    }

    /* Indigo */
    .aiko-bg-indigo-100 {
        background-color: #e0e7ff;
    }

    .aiko-bg-indigo-200 {
        background-color: #c7d2fe;
    }

    .aiko-bg-indigo-300 {
        background-color: #a5b4fc;
    }

    .aiko-bg-indigo-400 {
        background-color: #818cf8;
    }

    .aiko-bg-indigo-500 {
        background-color: #6366f1;
    }

    .aiko-bg-indigo-600 {
        background-color: #4f46e5;
    }

    .aiko-bg-indigo-700 {
        background-color: #4338ca;
    }

    .aiko-bg-indigo-800 {
        background-color: #3730a3;
    }

    .aiko-bg-indigo-900 {
        background-color: #312e81;
    }

    .aiko-border-indigo-200 {
        border-color: #c7d2fe;
    }

    .aiko-border-indigo-300 {
        border-color: #a5b4fc;
    }

    .aiko-text-indigo-600 {
        color: #4f46e5;
    }

    .aiko-focus\:ring-indigo-300:focus {
        box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
    }

    .aiko-focus\:ring-indigo-600:focus {
        box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.4);
    }

    .aiko-focus\:border-indigo-600:focus {
        border-color: #4f46e5;
    }

    .aiko-hover\:bg-indigo-700:hover {
        background-color: #4338ca;
    }

    .aiko-hover\:bg-indigo-800:hover {
        background-color: #3730a3;
    }

    .aiko-hover\:border-indigo-300:hover {
        border-color: #a5b4fc;
    }

    /* Teal */
    .aiko-bg-teal-100 {
        background-color: #ccfbf1;
    }

    .aiko-bg-teal-200 {
        background-color: #99f6e4;
    }

    .aiko-bg-teal-300 {
        background-color: #5eead4;
    }

    .aiko-bg-teal-400 {
        background-color: #2dd4bf;
    }

    .aiko-bg-teal-500 {
        background-color: #14b8a6;
    }

    .aiko-bg-teal-600 {
        background-color: #0d9488;
    }

    .aiko-bg-teal-700 {
        background-color: #0f766e;
    }

    .aiko-bg-teal-800 {
        background-color: #115e59;
    }

    .aiko-bg-teal-900 {
        background-color: #134e4a;
    }

    .aiko-border-teal-200 {
        border-color: #99f6e4;
    }

    .aiko-border-teal-300 {
        border-color: #5eead4;
    }

    .aiko-text-teal-600 {
        color: #0d9488;
    }

    .aiko-focus\:ring-teal-300:focus {
        box-shadow: 0 0 0 3px rgba(20, 184, 166, 0.2);
    }

    .aiko-focus\:ring-teal-600:focus {
        box-shadow: 0 0 0 3px rgba(13, 148, 136, 0.4);
    }

    .aiko-focus\:border-teal-600:focus {
        border-color: #0d9488;
    }

    .aiko-hover\:bg-teal-700:hover {
        background-color: #0f766e;
    }

    .aiko-hover\:bg-teal-800:hover {
        background-color: #115e59;
    }

    .aiko-hover\:border-teal-300:hover {
        border-color: #5eead4;
    }

    /* Orange */
    .aiko-bg-orange-100 {
        background-color: #ffedd5;
    }

    .aiko-bg-orange-200 {
        background-color: #fed7aa;
    }

    .aiko-bg-orange-300 {
        background-color: #fdba74;
    }

    .aiko-bg-orange-400 {
        background-color: #fb923c;
    }

    .aiko-bg-orange-500 {
        background-color: #f97316;
    }

    .aiko-bg-orange-600 {
        background-color: #ea580c;
    }

    .aiko-bg-orange-700 {
        background-color: #c2410c;
    }

    .aiko-bg-orange-800 {
        background-color: #9a3412;
    }

    .aiko-bg-orange-900 {
        background-color: #7c2d12;
    }

    .aiko-border-orange-200 {
        border-color: #fed7aa;
    }

    .aiko-border-orange-300 {
        border-color: #fdba74;
    }

    .aiko-text-orange-600 {
        color: #ea580c;
    }

    .aiko-focus\:ring-orange-300:focus {
        box-shadow: 0 0 0 3px rgba(249, 115, 22, 0.2);
    }

    .aiko-focus\:ring-orange-600:focus {
        box-shadow: 0 0 0 3px rgba(234, 88, 12, 0.4);
    }

    .aiko-focus\:border-orange-600:focus {
        border-color: #ea580c;
    }

    .aiko-hover\:bg-orange-700:hover {
        background-color: #c2410c;
    }

    .aiko-hover\:bg-orange-800:hover {
        background-color: #9a3412;
    }

    .aiko-hover\:border-orange-300:hover {
        border-color: #fdba74;
    }

    /* Customone */
    .aiko-bg-customone-50 {
        background-color: #e6f7ee;
    }

    .aiko-bg-customone-100 {
        background-color: #ccefdd;
    }

    .aiko-bg-customone-200 {
        background-color: #99dfbb;
    }

    .aiko-bg-customone-300 {
        background-color: #66cf99;
    }

    .aiko-bg-customone-400 {
        background-color: #33bf77;
    }

    .aiko-bg-customone-500 {
        background-color: #009143;
    }

    .aiko-bg-customone-600 {
        background-color: #007436;
    }

    .aiko-bg-customone-700 {
        background-color: #00572a;
    }

    .aiko-bg-customone-800 {
        background-color: #003a1d;
    }

    .aiko-bg-customone-900 {
        background-color: #001d0f;
    }

    .aiko-bg-customone-950 {
        background-color: #000e07;
    }

    .aiko-text-customone-500 {
        color: #009143;
    }

    .aiko-text-customone-600 {
        color: #007436;
    }

    .aiko-border-customone-200 {
        border-color: #99dfbb;
    }

    .aiko-border-customone-300 {
        border-color: #66cf99;
    }

    .aiko-focus\:ring-customone-300:focus {
        box-shadow: 0 0 0 3px rgba(0, 145, 67, 0.2);
    }

    .aiko-focus\:ring-customone-600:focus {
        box-shadow: 0 0 0 3px rgba(0, 145, 67, 0.4);
    }

    .aiko-focus\:border-customone-600:focus {
        border-color: #007436;
    }

    .aiko-hover\:bg-customone-700:hover {
        background-color: #00572a;
    }

    .aiko-hover\:bg-customone-800:hover {
        background-color: #003a1d;
    }

    .aiko-hover\:border-customone-300:hover {
        border-color: #66cf99;
    }

    /* Dark mode color variations removed */

    /* Borders */
    .aiko-border {
        border-width: 1px;
        border-style: solid;
    }

    .aiko-border-t {
        border-top-width: 1px;
        border-top-style: solid;
    }

    .aiko-rounded-tl-xl {
        border-top-left-radius: 0.75rem;
    }

    .aiko-rounded-b-xl {
        border-bottom-left-radius: 0.75rem !important;
        border-bottom-right-radius: 0.75rem !important;
    }

    .aiko-rounded-sm {
        border-radius: 0.125rem;
    }

    .aiko-rounded-md {
        border-radius: 0.375rem;
    }

    .aiko-rounded-lg {
        border-radius: 0.5rem;
    }

    .aiko-rounded-xl {
        border-radius: 0.75rem;
    }

    .aiko-rounded-t-xl {
        border-top-left-radius: 0.75rem;
        border-top-right-radius: 0.75rem;
    }

    .aiko-rounded-full {
        border-radius: 9999px;
    }

    .aiko-rounded-s-xl {
        border-start-start-radius: 0.75rem;
        border-end-start-radius: 0.75rem;
    }

    .aiko-rounded-br-xl {
        border-bottom-right-radius: 0.75rem;
    }

    .aiko-rounded-e-xl {
        border-start-end-radius: 0.75rem;
        border-end-end-radius: 0.75rem;
    }

    .aiko-rounded-es-xl {
        border-end-start-radius: 0.75rem;
    }

    /* Positioning */
    .aiko-relative {
        position: relative;
    }

    .aiko-absolute {
        position: absolute !important;
    }

    .aiko-fixed {
        position: fixed;
    }

    .aiko-top-0 {
        top: 0;
    }

    .aiko-top-1\/2 {
        top: 50%;
    }

    .aiko-top-2 {
        top: 0.5rem;
    }

    .aiko-bottom-0 {
        bottom: 0;
    }

    .aiko-left-0 {
        left: 0;
    }

    .aiko-left-1\/2 {
        left: 50%;
    }

    .aiko-right-0 {
        right: 0;
    }

    .-translate-x-1\/2 {
        transform: translateX(-50%);
    }

    .-translate-y-1\/2 {
        transform: translateY(-50%);
    }

    /* Display */
    .aiko-block {
        display: block;
    }

    .aiko-inline-flex {
        display: inline-flex;
    }

    .aiko-hidden {
        display: none;
    }

    /* Effects */
    .aiko-shadow {
        box-shadow:
            0 1px 3px 0 rgba(0, 0, 0, 0.1),
            0 1px 2px 0 rgba(0, 0, 0, 0.06);
    }

    .aiko-focus\:outline-none:focus {
        outline: 2px solid transparent;
        outline-offset: 2px;
    }

    .aiko-focus\:ring-4:focus {
        box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.5);
    }

    /* Interactivity */
    .aiko-cursor-pointer {
        cursor: pointer;
    }

    .aiko-cursor-grab {
        cursor: grab;
    }

    .aiko-disabled\:opacity-50:disabled {
        opacity: 0.5;
    }

    .aiko-disabled\:cursor-not-allowed:disabled {
        cursor: not-allowed;
    }

    .aiko-focus\:outline-none:focus {
        outline: none;
    }

    .aiko-sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
    }

    /* Special modifiers */
    .aiko-\!hover\:bg-sky-800:hover {
        background-color: #075985 !important;
    }

    .aiko-\!hover\:bg-blue-800:hover {
        background-color: #1e40af !important;
    }

    .aiko-\!hover\:bg-red-800:hover {
        background-color: #991b1b !important;
    }

    .aiko-\!hover\:bg-green-800:hover {
        background-color: #166534 !important;
    }

    .aiko-\!hover\:bg-yellow-800:hover {
        background-color: #854d0e !important;
    }

    .aiko-\!hover\:bg-purple-800:hover {
        background-color: #6b21a8 !important;
    }

    .aiko-\!hover\:bg-pink-800:hover {
        background-color: #9d174d !important;
    }

    .aiko-\!hover\:bg-customone-800:hover {
        background-color: #003a1d !important;
    }

    .aiko-\!hover\:bg-rose-800:hover {
        background-color: #9f1239 !important;
    }

    .aiko-\!hover\:bg-amber-800:hover {
        background-color: #92400e !important;
    }

    .aiko-\!hover\:bg-indigo-800:hover {
        background-color: #3730a3 !important;
    }

    .aiko-\!hover\:bg-teal-800:hover {
        background-color: #115e59 !important;
    }

    .aiko-\!hover\:bg-orange-800:hover {
        background-color: #9a3412 !important;
    }

    .aiko-\!hover\:border-sky-300:hover {
        border-color: #7dd3fc !important;
    }

    .aiko-\!hover\:border-blue-300:hover {
        border-color: #93c5fd !important;
    }

    .aiko-\!hover\:border-red-300:hover {
        border-color: #fca5a5 !important;
    }

    .aiko-\!hover\:border-green-300:hover {
        border-color: #86efac !important;
    }

    .aiko-\!hover\:border-yellow-300:hover {
        border-color: #fde047 !important;
    }

    .aiko-\!hover\:border-purple-300:hover {
        border-color: #d8b4fe !important;
    }

    .aiko-\!hover\:border-pink-300:hover {
        border-color: #f9a8d4 !important;
    }

    .aiko-\!hover\:border-rose-300:hover {
        border-color: #fda4af !important;
    }

    .aiko-\!hover\:border-amber-300:hover {
        border-color: #fcd34d !important;
    }

    .aiko-\!hover\:border-indigo-300:hover {
        border-color: #a5b4fc !important;
    }

    .aiko-\!hover\:border-teal-300:hover {
        border-color: #5eead4 !important;
    }

    .aiko-\!hover\:border-orange-300:hover {
        border-color: #fdba74 !important;
    }

    /* Overflow */
    .aiko-overflow-hidden {
        overflow: hidden;
    }

    .aiko-overflow-y-auto {
        overflow-y: auto;
    }

    .aiko-overflow-y-hidden {
        overflow-y: hidden;
    }

    /* Transitions */
    .aiko-transition-all {
        transition-property: all;
    }

    .aiko-duration-300 {
        transition-duration: 300ms;
    }

    /* Scrollbar (custom) */
    .aiko-scrollbar-thin {
        scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        scrollbar-width: thin;
    }

    .aiko-scrollbar-thin::-webkit-scrollbar-track {
        border-radius: 25px !important;
        cursor: pointer !important;
        background: transparent !important;
    }

    .aiko-scrollbar-thin::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.1) !important;
        border-radius: 25px !important;
    }

    /* Animation classes */
    .aiko-animate__animated {
        animation-duration: 1s;
        animation-fill-mode: both;
    }

    .aiko-animate__faster {
        animation-duration: 0.5s;
    }

    .aiko-animate__delay-1s {
        animation-delay: 1s;
    }

    .aiko-animate__bounceIn {
        animation-name: bounceIn;
    }

    .aiko-animate__bounceInUp {
        animation-name: bounceInUp;
    }

    .aiko-animate__bounceOutDown {
        animation-name: bounceOutDown;
    }

    @keyframes bounceIn {
        from,
        20%,
        40%,
        60%,
        80%,
        to {
            animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        }

        0% {
            opacity: 0;
            transform: scale3d(0.3, 0.3, 0.3);
        }

        20% {
            transform: scale3d(1.1, 1.1, 1.1);
        }

        40% {
            transform: scale3d(0.9, 0.9, 0.9);
        }

        60% {
            opacity: 1;
            transform: scale3d(1.03, 1.03, 1.03);
        }

        80% {
            transform: scale3d(0.97, 0.97, 0.97);
        }

        to {
            opacity: 1;
            transform: scale3d(1, 1, 1);
        }
    }

    @keyframes bounceInUp {
        from,
        60%,
        75%,
        90%,
        to {
            animation-timing-function: cubic-bezier(0.215, 0.61, 0.355, 1);
        }

        from {
            opacity: 0;
            transform: translate3d(0, 3000px, 0) scaleY(5);
        }

        60% {
            opacity: 1;
            transform: translate3d(0, -20px, 0) scaleY(0.9);
        }

        75% {
            transform: translate3d(0, 10px, 0) scaleY(0.95);
        }

        90% {
            transform: translate3d(0, -5px, 0) scaleY(0.985);
        }

        to {
            transform: translate3d(0, 0, 0);
        }
    }

    @keyframes bounceOutDown {
        20% {
            transform: translate3d(0, 10px, 0) scaleY(0.985);
        }

        40%,
        45% {
            opacity: 1;
            transform: translate3d(0, -20px, 0) scaleY(0.9);
        }

        to {
            opacity: 0;
            transform: translate3d(0, 2000px, 0) scaleY(3);
        }
    }

    /* Dark mode classes removed */

    /* Chat specific styles */
    .chat-button {
        z-index: 9999;
        position: fixed;
    }

    .aiko-chat-window {
        width: 400px;
        z-index: 9999;
        box-shadow:
            0 4px 6px -1px rgba(0, 0, 0, 0.1),
            0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }

    /* h-[70vh] */

    .aiko-h-\[70vh\] {
        height: 70vh;
    }

    .aiko-space-y-4 > :not([hidden]) ~ :not([hidden]) {
        --tw-space-y-reverse: 0;
        margin-top: 15px;
        margin-bottom: 15px;
    }

    button:hover {
        opacity: 0.8;
        box-shadow:
            rgba(50, 50, 93, 0.25) 0px 2px 5px -1px,
            rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;
    }

    .aiko-send-button {
        outline: none;
        border: none;
    }

    .aiko-border-gray-100 {
        border-color: #dcdddf;
    }

    .aiko-line-clamp-1 {
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
        line-clamp: 1;
    }

    .aiko-truncate {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}
