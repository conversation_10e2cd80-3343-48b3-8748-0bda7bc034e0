import './assets/custom.scss'
import { Icon } from '@iconify/vue'
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './pages/index.vue'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import { useAuthStore } from './stores/auth'
import { useChatStore } from './stores/chat'
import { useSettingsStore } from './stores/settings'

// Add type declaration for window object
declare global {
  interface Window {
    LLMRagChatbot: any
  }
}

// Create a class for the chatbot that can be instantiated from another project
class LLMRagChatbot {
  private app: any
  private pinia: any
  private div: HTMLElement
  private authStore: any
  private chatStore: any
  private settingsStore: any
  private isInitialized: boolean = false

  constructor(
    options: {
      tenantId?: string
      envId?: string
      env?: string
      token?: string
      refresh_token?: string
      liffId?: string
      position?: { left?: string; top?: string }
      windowPosition?: { left?: string }
    } = {},
  ) {
    // Create container div
    this.div = document.createElement('div')
    this.div.id = 'llm-rag-chatbot'

    // Check if document.body exists
    if (document.body) {
      document.body.appendChild(this.div)
      this.initializeApp(options)
    } else {
      // If document.body doesn't exist yet, wait for it
      this.waitForBody(options)
    }
  }

  /**
   * Wait for document.body to be available before initializing
   */
  private waitForBody(options: any) {
    // Use DOMContentLoaded or a timeout to wait for body
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', () => {
        document.body.appendChild(this.div)
        this.initializeApp(options)
      })
    } else {
      // If DOMContentLoaded has already fired but body is still null,
      // use a short timeout to check again
      const checkBodyInterval = setInterval(() => {
        if (document.body) {
          clearInterval(checkBodyInterval)
          document.body.appendChild(this.div)
          this.initializeApp(options)
        }
      }, 50) // Check every 50ms
    }
  }

  /**
   * Initialize the Vue app
   */
  private initializeApp(options: any) {
    // Initialize Vue app
    this.app = createApp(App)
    this.pinia = createPinia()
    this.pinia.use(piniaPluginPersistedstate)
    this.app.use(this.pinia)
    this.app.component('Icon', Icon)

    // Mount the app
    this.app.mount(this.div)

    // Get store instances
    this.authStore = useAuthStore()
    this.chatStore = useChatStore()
    this.settingsStore = useSettingsStore()

    // Initialize with options if provided
    if (options) {
      this.initialize(options)
    }
  }

  /**
   * Initialize the chatbot with custom options
   */
  initialize(
    options: {
      tenantId?: string
      envId?: string
      env?: string
      token?: string
      refresh_token?: string
      liffId?: string
      position?: { left?: string; top?: string }
      windowPosition?: { left?: string }
    } = {},
  ) {
    // Set auth parameters
    if (options.tenantId) this.authStore.tenantId = options.tenantId
    if (options.envId) this.authStore.envId = options.envId
    if (options.env) this.authStore.env = options.env
    if (options.refresh_token) {
      this.authStore.setChatbotAuths(
        this.authStore.tenantId,
        this.authStore.envId,
        this.authStore.env,
        {
          refresh_token: options.refresh_token,
          token: options.token,
        },
      )
    }
    if (options.liffId) this.authStore.liffId = options.liffId

    // Initialize chatbot
    this.authStore.initChatbot(options)
    // this.settingsStore.getBasicSettings()

    // Handle login if needed
    if (!this.authStore.isLoggedIn && !this.authStore.isNeedLogin) {
      this.authStore.guestLogin()
    }

    // Initialize chat
    this.chatStore.chatInit()

    // If liffId has a value, set fullscreen mode by default and open the chat
    if (this.authStore.liffId) {
      this.settingsStore.isFullScreen = true
      this.settingsStore.isOpen = true
    }

    // Set position if provided
    if (options.position) {
      this.setPosition(options.position)
    }

    // Set window position if provided
    if (options.windowPosition) {
      this.setWindowPosition(options.windowPosition)
    }

    this.isInitialized = true
    return this
  }

  /**
   * Reload the chatbot with new authentication parameters
   * This is useful when you need to switch tenants, environments or users from an admin page
   */
  reload({
    tenantId,
    envId,
    env,
    token,
    refresh_token,
    liffId,
  }: {
    tenantId?: string
    envId?: string
    env?: string
    token?: string
    refresh_token?: string
    liffId?: string
  }) {
    console.log('reload', { tenantId, envId, env, token, refresh_token, liffId })
    // Update auth parameters
    if (tenantId !== undefined) this.authStore.tenantId = tenantId
    if (envId !== undefined) this.authStore.envId = envId
    if (env !== undefined) this.authStore.env = env
    if (refresh_token !== undefined) {
      this.authStore.setChatbotAuths(
        this.authStore.tenantId,
        this.authStore.envId,
        this.authStore.env,
        {
          refresh_token,
          token,
        },
      )
    }

    // Reinitialize chatbot with new parameters
    this.authStore.initChatbot({
      tenantId,
      envId,
      env,
      token,
      refresh_token,
      liffId,
    })

    // If liffId has a value, set fullscreen mode by default and open the chat
    if (this.authStore.liffId) {
      this.settingsStore.isFullScreen = true
      this.settingsStore.isOpen = true
    }

    // Refresh settings based on new parameters
    this.settingsStore.getBasicSettings()

    // Reset chat history
    this.chatStore.chatInit()

    return this
  }

  /**
   * Update authentication parameters without reloading the entire chatbot
   * This allows changing credentials without resetting the chat history
   */
  updateAuth({
    tenantId,
    envId,
    env,
    token,
    refresh_token,
    liffId,
  }: {
    tenantId?: string
    envId?: string
    env?: string
    token?: string
    refresh_token?: string
    liffId?: string
  }) {
    // Update auth parameters
    if (tenantId !== undefined) this.authStore.tenantId = tenantId
    if (envId !== undefined) this.authStore.envId = envId
    if (env !== undefined) this.authStore.env = env
    if (refresh_token !== undefined) {
      this.authStore.setChatbotAuths(
        this.authStore.tenantId,
        this.authStore.envId,
        this.authStore.env,
        {
          refresh_token,
          token,
        },
      )
    }
    if (liffId !== undefined) this.authStore.liffId = liffId

    // If liffId has a value, set fullscreen mode by default and open the chat
    if (this.authStore.liffId) {
      this.settingsStore.isFullScreen = true
      this.settingsStore.isOpen = true
    }

    // Refresh settings based on new parameters
    this.settingsStore.getBasicSettings()

    return this
  }

  /**
   * Open the chatbot
   */
  open() {
    this.settingsStore.isOpen = true
    this.settingsStore.getBasicSettings()
    return this
  }

  /**
   * Close the chatbot
   */
  close() {
    this.settingsStore.isOpen = false
    return this
  }

  /**
   * Toggle the chatbot open/closed state
   */
  toggle() {
    this.settingsStore.isOpen = !this.settingsStore.isOpen
    if (this.settingsStore.isOpen) {
      this.settingsStore.getBasicSettings()
    }
    return this
  }

  /**
   * Send a message to the chatbot
   */
  async sendMessage(message: string) {
    if (!this.isInitialized) {
      console.error('Chatbot not initialized. Call initialize() first.')
      return this
    }
    await this.chatStore.chat(message)
    return this
  }

  /**
   * Reset the chat history
   */
  resetChat() {
    this.chatStore.chatInit()
    return this
  }

  /**
   * Get the current chat history
   */
  getChatHistory() {
    return this.chatStore.chats
  }

  /**
   * Set the position of the chatbot button
   */
  setPosition(position: { left?: string; top?: string }) {
    const chatbotElement = document.querySelector('#llm-rag-chatbot button') as HTMLElement
    if (chatbotElement) {
      if (position.left) chatbotElement.style.left = position.left
      if (position.top) chatbotElement.style.top = position.top
    }
    return this
  }

  /**
   * Set the position of the chatbot window
   */
  setWindowPosition(position: { left?: string }) {
    const chatWindow = document.querySelector('#llm-rag-chatbot .aiko-chat-window') as HTMLElement
    if (chatWindow && position.left) {
      chatWindow.style.left = position.left
    }
    return this
  }

  /**
   * Check if the chatbot is open
   */
  isOpen() {
    return this.settingsStore.isOpen
  }

  /**
   * Hide the chat bubble
   * This will hide the floating chat button but keep the chat window if it's open
   */
  hideBubble() {
    this.settingsStore.isBubbleVisible = false
    return this
  }

  /**
   * Show the chat bubble
   */
  showBubble() {
    this.settingsStore.isBubbleVisible = true
    return this
  }

  /**
   * Toggle the visibility of the chat bubble
   */
  toggleBubble() {
    this.settingsStore.isBubbleVisible = !this.settingsStore.isBubbleVisible
    return this
  }

  /**
   * Check if the chat bubble is visible
   */
  isBubbleVisible() {
    return this.settingsStore.isBubbleVisible
  }

  /**
   * Toggle full-screen mode for the chat window
   */
  toggleFullScreen() {
    this.settingsStore.isFullScreen = !this.settingsStore.isFullScreen

    // Handle body scroll based on fullscreen state
    if (this.settingsStore.isFullScreen) {
      document.body.classList.add('body-no-scroll')
    } else {
      document.body.classList.remove('body-no-scroll')
    }

    return this
  }

  /**
   * Enable full-screen mode for the chat window
   */
  enableFullScreen() {
    this.settingsStore.isFullScreen = true
    document.body.classList.add('body-no-scroll')
    return this
  }

  /**
   * Disable full-screen mode for the chat window
   */
  disableFullScreen() {
    this.settingsStore.isFullScreen = false
    document.body.classList.remove('body-no-scroll')
    return this
  }

  /**
   * Check if the chat window is in full-screen mode
   */
  isFullScreen() {
    return this.settingsStore.isFullScreen
  }

  /**
   * Logout and clear all data in all stores
   * This will reset all stores to their default state
   */
  logout() {
    this.settingsStore.logout()
    return this
  }

  /**
   * Get the chatbot instance
   */
  getInstance() {
    return {
      authStore: this.authStore,
      chatStore: this.chatStore,
      settingsStore: this.settingsStore,
      app: this.app,
    }
  }
}

// Create a default instance
const defaultInstance = new LLMRagChatbot()

// Expose the class and default instance in the window object
window.LLMRagChatbot = {
  Class: LLMRagChatbot,
  instance: defaultInstance,
}
