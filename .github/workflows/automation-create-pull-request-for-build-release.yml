name: Create a pull request for release to production

on:
  push:
    branches: [main]

jobs:
  create-pr-pnl:
    strategy:
      matrix:
        branch: ['release']
    runs-on: ubuntu-latest
    env:
      GH_TOKEN: ${{ secrets.PAT }}
    steps:
      - name: 'Checkout GitHub Action'
        uses: actions/checkout@v2
        with:
          token: ${{ secrets.PAT }}
          submodules: recursive
      - name: Check if PR exists
        id: checkPNL
        env:
          GITHUB_TOKEN: ${{ secrets.PAT }}
        run: |
          prs=$(gh pr list \
              --repo "$GITHUB_REPOSITORY" \
              --json baseRefName,headRefName \
              --jq '
                  map(select(.baseRefName ==  "${{ matrix.branch }}" and .headRefName == "develop"))
                  | length
              ')
          if ((prs > 0)); then
              echo "skip=true" >> "$GITHUB_OUTPUT"
          fi
      - name: Create pull request for deploy
        if: '!steps.checkPNL.outputs.skip'
        run: |
          gh pr create -B  "${{ matrix.branch }}" -t "[${{ matrix.branch }}] Build and deploy to production" -b "automated pull request for deploy to production" -a "@me"
