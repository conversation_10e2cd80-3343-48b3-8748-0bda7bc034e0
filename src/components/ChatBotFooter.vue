<template>
  <div
    class="chatbot-footer"
    :style="{
      textAlign: 'left',
      fontSize: '9px',
      paddingTop: '3px',
      paddingBottom: '3px',
      paddingLeft: '10px',
    }"
  >
    スマート公共ラボ AIコンシェルジュ.v.{{ version }}
  </div>
</template>

<script setup lang="ts">
const version = import.meta.env.VITE_VERSION
</script>

<style>
.chatbot-footer {
  user-select: text !important;
  cursor: auto !important;
  color: #888;
}

/* Dark mode styles for footer */
.dark .chatbot-footer {
  color: #aaa;
}
</style>
