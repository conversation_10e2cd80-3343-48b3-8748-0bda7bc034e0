<template>
  <div
    class="aiko-w-full aiko-text-center aiko-py-3 aiko-text-xs aiko-cursor-pointer rechat-button-transparent"
    :class="classButton"
    @click="chatStore.chatInit()"
  >
    もう一度話す
  </div>
</template>

<script setup lang="ts">
import { useSettingsStore } from '@/stores/settings'
import { computed, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useChatStore } from '@/stores/chat'

const chatStore = useChatStore()
const settingsStore = useSettingsStore()
const { color_primary, chatbot_name } = storeToRefs(settingsStore)

const { query, loadings } = storeToRefs(chatStore)

const classButton = computed(() => {
  return [
    `aiko-bg-${color_primary.value}-600`,
    `aiko-text-white`,
    `aiko-hover:bg-${color_primary.value}-700`,
    `aiko-focus:ring-${color_primary}-300`,
  ]
})
</script>

<style>
.rechat-button-transparent {
  background-color: rgba(var(--color-primary-rgb, 14, 165, 233), 0.8) !important;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  border-bottom-left-radius: 0.75rem;
  border-bottom-right-radius: 0.75rem;
}
</style>
