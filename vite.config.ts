import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'
import cssInjectedByJsPlugin from 'vite-plugin-css-injected-by-js'

// https://vite.dev/config/
export default defineConfig(({}) => {
  return {
    server: {
      port: 3000,
    },
    define: {
      'process.env': {},
    },
    plugins: [vue(), vueDevTools(), cssInjectedByJsPlugin()],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
      },
    },
    build: {
      lib: {
        // if run dev, entry is src/dev.ts
        // if run build, entry is src/rag-chatbot-embed.ts
        entry: 'src/rag-chatbot-embed.ts',
        name: 'llm-rag-chatbot-embed',
        formats: ['umd'],
        // rename umd to min.js
        fileName: (format) => `chatbot.min.js`,
      },
    },
  }
})
